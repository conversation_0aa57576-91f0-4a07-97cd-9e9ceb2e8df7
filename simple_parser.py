#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版本的display数据解析器
"""

import re
import pandas as pd
import os

def parse_packet_data(file_path):
    """解析drDefinePacket数据"""
    display_packets = []
    psb_packets = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        print("文件读取成功")

        # 查找所有drDefinePacket部分
        in_packet_section = False
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line = line.strip()

            if 'drDefinePacket(' in line:
                in_packet_section = True
                print(f"找到drDefinePacket在第{i+1}行")
                continue

            if in_packet_section and line == ')':
                in_packet_section = False
                print(f"drDefinePacket结束在第{i+1}行")
                continue

            if in_packet_section and line and not line.startswith(';'):
                # 解析数据行
                # 格式: ( DisplayName  PacketName  Stipple  LineStyle  Fill  Outline  [FillStyle])
                match = re.match(r'\s*\(\s*(\w+)\s+(\w+)\s+(\w+)\s+(\w+)\s+(\w+)\s+(\w+)\s+(\w+)\s*\)', line)

                if match:
                    display_name, packet_name, stipple, line_style, fill, outline, fill_style = match.groups()

                    data_dict = {
                        'DisplayName': display_name,
                        'PacketName': packet_name,
                        'Stipple': stipple,
                        'LineStyle': line_style,
                        'Fill': fill,
                        'Outline': outline,
                        'FillStyle': fill_style
                    }

                    if display_name == 'display':
                        display_packets.append(data_dict)
                    elif display_name == 'psb':
                        psb_packets.append(data_dict)

        print(f"解析完成: Display packets: {len(display_packets)}, PSB packets: {len(psb_packets)}")
        return display_packets, psb_packets

    except Exception as e:
        print(f"解析错误: {e}")
        return [], []

def parse_color_data(file_path):
    """解析drDefineColor数据"""
    display_colors = []
    psb_colors = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        in_color_section = False
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line = line.strip()

            if 'drDefineColor(' in line:
                in_color_section = True
                continue

            if in_color_section and line == ')':
                in_color_section = False
                continue

            if in_color_section and line and not line.startswith(';'):
                # 解析颜色数据行
                # 格式: ( DisplayName   ColorsName   Red      Green    Blue     [Blink])
                match = re.match(r'\s*\(\s*(\w+)\s+(\w+)\s+(\d+)\s+(\d+)\s+(\d+)\s*(?:\s+(\w+))?\s*\)', line)

                if match:
                    display_name, color_name, red, green, blue = match.groups()[:5]
                    blink = match.group(6) if len(match.groups()) > 5 and match.group(6) else None

                    color_dict = {
                        'DisplayName': display_name,
                        'ColorName': color_name,
                        'Red': int(red),
                        'Green': int(green),
                        'Blue': int(blue),
                        'Blink': blink
                    }

                    if display_name == 'display':
                        display_colors.append(color_dict)
                    elif display_name == 'psb':
                        psb_colors.append(color_dict)

        print(f"颜色解析完成: Display colors: {len(display_colors)}, PSB colors: {len(psb_colors)}")
        return display_colors, psb_colors

    except Exception as e:
        print(f"颜色解析错误: {e}")
        return [], []

def parse_line_style_data(file_path):
    """解析drDefineLineStyle数据"""
    display_line_styles = []
    psb_line_styles = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        in_line_style_section = False
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line = line.strip()

            if 'drDefineLineStyle(' in line:
                in_line_style_section = True
                continue

            if in_line_style_section and line == ')':
                in_line_style_section = False
                continue

            if in_line_style_section and line and not line.startswith(';'):
                # 解析线型数据行
                # 格式: ( DisplayName   LineStyle    Size       Pattern   )
                match = re.match(r'\s*\(\s*(\w+)\s+(\w+)\s+(\d+)\s+\((.*?)\)\s*\)', line)

                if match:
                    display_name, line_style_name, size, pattern = match.groups()

                    line_style_dict = {
                        'DisplayName': display_name,
                        'LineStyleName': line_style_name,
                        'Size': int(size),
                        'Pattern': pattern.strip()
                    }

                    if display_name == 'display':
                        display_line_styles.append(line_style_dict)
                    elif display_name == 'psb':
                        psb_line_styles.append(line_style_dict)

        print(f"线型解析完成: Display line styles: {len(display_line_styles)}, PSB line styles: {len(psb_line_styles)}")
        return display_line_styles, psb_line_styles

    except Exception as e:
        print(f"线型解析错误: {e}")
        return [], []

def save_to_excel(all_data, output_file):
    """保存所有数据到Excel"""
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # Display数据
            if all_data['display']['packets']:
                df = pd.DataFrame(all_data['display']['packets'])
                df.to_excel(writer, sheet_name='Display_Packets', index=False)
                print(f"Display Packets已保存，共{len(all_data['display']['packets'])}条记录")

            if all_data['display']['colors']:
                df = pd.DataFrame(all_data['display']['colors'])
                df.to_excel(writer, sheet_name='Display_Colors', index=False)
                print(f"Display Colors已保存，共{len(all_data['display']['colors'])}条记录")

            if all_data['display']['line_styles']:
                df = pd.DataFrame(all_data['display']['line_styles'])
                df.to_excel(writer, sheet_name='Display_LineStyles', index=False)
                print(f"Display LineStyles已保存，共{len(all_data['display']['line_styles'])}条记录")

            # PSB数据
            if all_data['psb']['packets']:
                df = pd.DataFrame(all_data['psb']['packets'])
                df.to_excel(writer, sheet_name='PSB_Packets', index=False)
                print(f"PSB Packets已保存，共{len(all_data['psb']['packets'])}条记录")

            if all_data['psb']['colors']:
                df = pd.DataFrame(all_data['psb']['colors'])
                df.to_excel(writer, sheet_name='PSB_Colors', index=False)
                print(f"PSB Colors已保存，共{len(all_data['psb']['colors'])}条记录")

            if all_data['psb']['line_styles']:
                df = pd.DataFrame(all_data['psb']['line_styles'])
                df.to_excel(writer, sheet_name='PSB_LineStyles', index=False)
                print(f"PSB LineStyles已保存，共{len(all_data['psb']['line_styles'])}条记录")

        print(f"Excel文件已保存: {output_file}")

    except Exception as e:
        print(f"保存Excel时出错: {e}")

def main():
    input_file = 'u22_display.txt'
    output_file = 'u22_display_complete.xlsx'

    print(f"开始处理文件: {input_file}")

    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return

    # 解析所有数据
    print("\n=== 解析Packet数据 ===")
    display_packets, psb_packets = parse_packet_data(input_file)

    print("\n=== 解析Color数据 ===")
    display_colors, psb_colors = parse_color_data(input_file)

    print("\n=== 解析LineStyle数据 ===")
    display_line_styles, psb_line_styles = parse_line_style_data(input_file)

    # 组织数据
    all_data = {
        'display': {
            'packets': display_packets,
            'colors': display_colors,
            'line_styles': display_line_styles
        },
        'psb': {
            'packets': psb_packets,
            'colors': psb_colors,
            'line_styles': psb_line_styles
        }
    }

    # 保存到Excel
    save_to_excel(all_data, output_file)

    # 显示统计信息
    print(f"\n=== 数据统计 ===")
    print(f"Display - Packets: {len(display_packets)}, Colors: {len(display_colors)}, LineStyles: {len(display_line_styles)}")
    print(f"PSB - Packets: {len(psb_packets)}, Colors: {len(psb_colors)}, LineStyles: {len(psb_line_styles)}")

    # 显示示例数据
    if display_packets:
        print(f"\nDisplay Packets示例:")
        for i, item in enumerate(display_packets[:2]):
            print(f"  {i+1}. {item}")

    if psb_packets:
        print(f"\nPSB Packets示例:")
        for i, item in enumerate(psb_packets[:2]):
            print(f"  {i+1}. {item}")

    if display_colors:
        print(f"\nDisplay Colors示例:")
        for i, item in enumerate(display_colors[:2]):
            print(f"  {i+1}. {item}")

    print(f"\n处理完成！数据已保存到 {output_file}")
    print("Excel文件包含以下工作表:")
    print("- Display_Packets: Display的包定义")
    print("- Display_Colors: Display的颜色定义")
    print("- Display_LineStyles: Display的线型定义")
    print("- PSB_Packets: PSB的包定义")
    print("- PSB_Colors: PSB的颜色定义")
    print("- PSB_LineStyles: PSB的线型定义")

if __name__ == "__main__":
    main()
