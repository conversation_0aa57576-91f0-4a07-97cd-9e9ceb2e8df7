import re
import pandas as pd

def parse_dr_define_packet(file_path):
    """解析u22_display.txt文件中的drDefinePacket模块"""
    with open(file_path, 'r') as file:
        content = file.read()

    # 找到所有drDefinePacket块
    dr_define_blocks = re.findall(r'\(drDefinePacket.*?\)', content, re.DOTALL)
    
    display_data = []
    psb_data = []

    for block in dr_define_blocks:
        # 找到所有括号内的条目
        entries = re.findall(r'\(.*?\)', block, re.DOTALL)
        
        for entry in entries:
            # 去除括号
            entry_content = entry[1:-1].strip()
            
            # 分割条目中的各个部分
            parts = entry_content.split()
            
            if not parts:
                continue
                
            # 判断是display还是psb条目
            if parts[0].lower() == 'display':
                display_data.append({
                    'raw_entry': entry_content,
                    'type': parts[0],
                    'name': parts[1] if len(parts) > 1 else None,
                    'pattern': parts[2] if len(parts) > 2 else None,
                    'style': parts[3] if len(parts) > 3 else None,
                    'color1': parts[4] if len(parts) > 4 else None,
                    'color2': parts[5] if len(parts) > 5 else None,
                    'display_type': parts[6] if len(parts) > 6 else None
                })
            elif parts[0].lower() == 'psb':
                psb_data.append({
                    'raw_entry': entry_content,
                    'type': parts[0],
                    'name': parts[1] if len(parts) > 1 else None,
                    'pattern': parts[2] if len(parts) > 2 else None,
                    'style': parts[3] if len(parts) > 3 else None,
                    'color1': parts[4] if len(parts) > 4 else None,
                    'color2': parts[5] if len(parts) > 5 else None,
                    'display_type': parts[6] if len(parts) > 6 else None
                })

    return display_data, psb_data

def save_to_excel(display_data, psb_data, output_path):
    """将解析的数据保存到Excel文件中"""
    # 创建DataFrame
    df_display = pd.DataFrame(display_data)
    df_psb = pd.DataFrame(psb_data)

    # 写入Excel文件
    with pd.ExcelWriter(output_path) as writer:
        df_display.to_excel(writer, sheet_name='Display', index=False)
        df_psb.to_excel(writer, sheet_name='PSB', index=False)

    print(f"数据已成功保存到 {output_path}")

if __name__ == "__main__":
    input_file = "u22_display.txt"
    output_file = "parsed_output.xlsx"
    
    display_data, psb_data = parse_dr_define_packet(input_file)
    save_to_excel(display_data, psb_data, output_file)