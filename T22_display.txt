;*****************************************************************************
; TSMC 22nm Layout Editor (Virtuoso) Display File - display.drf, 11/26/2021
; 1P10M PROCESS
; Technology File Document: T-N22-CL-LE-002 Ver 1.5a
; Tech File Ver: v1.5a
; Used for Virtuoso Virtuoso 6.1.5.500.9 or higher
;*****************************************************************************
; DISCLAIMER
;
; The information contained herein is provided by TSMC on an "AS IS" basis
; without any warranty, and TSMC has no obligation to support or otherwise
; maintain the information.  TSMC disclaims any representation that the
; information does not infringe any intellectual property rights or proprietary
; rights of any third parties.  There are no other warranties given by TSMC,
; whether express, implied or statutory, including, without limitation, implied
; warranties of merchantability and fitness for a particular purpose.
;
; STATEMENT OF USE
;
; This information contains confidential and proprietary information of TSMC.
; No part of this information may be reproduced, transmitted, transcribed,
; stored in a retrieval system, or translated into any human or computer
; language, in any form or by any means, electronic, mechanical, magnetic,
; optical, chemical, manual, or otherwise, without the prior written permission
; of TSMC.  This information was prepared for informational purpose and is for
; use by TSMC's customers only.  TSMC reserves the right to make changes in the
; information at any time and without notice.
;*****************************************************************************

drDefineDisplay(
;( DisplayName )
 ( display    )
)


; -----------------------------------------------------------------
; ------ Display information for the display device 'display'. ------
; -----------------------------------------------------------------
drDefineColor(
;( DisplayName   ColorName       Red   Green   Blue   Blink )
 ( display       white           255    255    255  )
 ( display       whiteB          255    255    255     t)
 ( display       silver          217    230    255  )
 ( display       silverB         217    230    255     t)
 ( display       cream           255    255    204  )
 ( display       pink            255    191    242  )
 ( display       pinkB           255    191    242     t)
 ( display       magenta         255    0      255  )
 ( display       lime            0      255    0    )
 ( display       tan             255    230    191  )
 ( display       cyan            0      230    230  )
 ( display       gray            204    204    217  )
 ( display       yellow          255    255    0    )
 ( display       yellowB         255    255    0       t)
 ( display       orange          255    128    0    )
 ( display       orangeB         255    128    0       t)
 ( display       red             255    0      0    )
 ( display       redB            255    0      0       t)
 ( display       purple          153    0      230  )
 ( display       purpleB         153    0      230     t)
 ( display       green           0      204    102  )
 ( display       greenB          0      204    102     t)
 ( display       brown           191    64     38   )
 ( display       brownB          191    64     38      t)
 ( display       blue            0      0      255  )
 ( display       blueB           0      0      255     t)
 ( display       slate           140    140    166  )
 ( display       gold            217    204    0    )
 ( display       goldB           217    204    0       t)
 ( display       maroon          230    31     13   )
 ( display       violet          94     0      230  )
 ( display       forest          38     140    107  )
 ( display       chocolate       128    38     38   )
 ( display       navy            51     51     153  )
 ( display       black           0      0      0    )
 ( display       winBack         224    224    224  )
 ( display       winFore         128    0      0    )
 ( display       winText         51     51     51   )
 ( display       winColor1       166    166    166  )
 ( display       winColor2       115    115    115  )
 ( display       winColor3       189    204    204  )
 ( display       winColor4       204    204    204  )
 ( display       winColor5       199    199    199  )
 ( display       winColor5B      199    199    199     t)
 ( display       joy1            0      204    242  )
 ( display       cadetBlue       57     191    255  )
)

drDefineStipple(
;( DisplayName   StippleName     Bitmap )
 ( display      blank        (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      solid        (
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                             ) )
 ( display      dots         (
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      hLine        (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      vLine        (
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                             ) )
 ( display      cross        (
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                             ) )
 ( display      grid         (
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                             ) )
 ( display      slash        (
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                             ) )
 ( display      backSlash    (
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0)
                              (0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1)
                             ) )
 ( display      hZigZag      (
                              (1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0)
                              (0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0)
                              (0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0)
                              (0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0)
                              (0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      vZigZag      (
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                             ) )
 ( display      hCurb        (
                              (0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 1 1 1)
                              (0 0 0 1 0 0 0 1)
                              (0 0 0 1 0 0 0 1)
                              (1 1 1 1 0 0 0 1)
                              (0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0)
                             ) )
 ( display      vCurb        (
                              (0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 1 0 0)
                              (0 0 1 1 1 1 0 0)
                              (0 0 1 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0)
                              (0 0 1 1 1 1 0 0)
                             ) )
 ( display      brick        (
                              (1 0 0 0 1 0 0 1 0 0 0 1)
                              (0 0 1 0 0 0 0 0 0 1 0 0)
                              (1 0 0 0 1 0 0 1 0 0 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 0 0 0 1 0 0 1 0 0 0 1)
                              (0 0 1 0 0 0 0 0 0 1 0 0)
                             ) )
 ( display      dagger       (
                              (0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0)
                              (1 1 1 1 1 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 1 1 1 1 1)
                              (0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 1 0 0)
                             ) )
 ( display      triangle     (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 1 0 0 0 1 0 0 0 0 0 0 0 0)
                              (1 1 1 1 1 1 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 1 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 1 0)
                              (0 0 0 0 0 0 0 1 1 1 1 1 1 1)
                             ) )
 ( display      x            (
                              (1 0 0 0)
                              (0 1 0 1)
                              (0 0 1 0)
                              (0 1 0 1)
                             ) )
 ( display      dot1         (
                              (1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display       dot2         (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      dot3         (
                              (1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      dot4         (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      stipple0     (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      stipple1     (
                              (1 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 1)
                             ) )
 ( display      stipple2     (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 1)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 1 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (1 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      stipple3     (
                              (1 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 1 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 1 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 1)
                             ) )
 ( display      z2           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      z3           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      y3           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      z4           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      y4           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      y5           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      z5           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      z6           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      y6           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      z7           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      y7           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      z8           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      y8           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      z9           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      y9           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      z10          (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 1 1 1 1 1 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 1 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      zd           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 1 1 1 1 1 1 1 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 1 1 0 0 0 0 0 0 1 1 1 1 0 0)
                              (0 0 0 0 0 1 1 0 0 0 0 0 1 0 0 0 1 0 0)
                              (0 0 0 0 1 0 0 1 0 0 0 1 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0)
                              (0 0 1 1 1 1 1 1 1 1 0 0 0 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      yd           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 1 0 0)
                              (0 0 0 0 0 1 0 1 0 0 0 0 0 1 1 1 1 0 0)
                              (0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 1 0 0)
                              (0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 0 0)
                              (0 0 0 0 1 0 0 0 0 0 0 1 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 0 0 0 0 0 0 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      ut           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 1 1 1 1 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0)
                              (0 0 0 0 1 1 1 1 1 0 0 0 0 0 0 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      u            (
                              (0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 0 0 1 1 1 1 1 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      ud           (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 0 1 0 0 0 1 1 1 1 0 0)
                              (0 0 1 0 0 0 0 0 1 0 0 1 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 0 1 0 1 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 0 1 0 1 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 0 1 0 0 1 0 0 0 1 0 0)
                              (0 0 0 1 1 1 1 1 0 0 0 0 1 1 1 1 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
 ( display      utv          (
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 1 0 0)
                              (0 0 1 0 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0)
                              (0 0 1 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0 0)
                              (0 0 0 1 1 1 1 0 0 0 0 0 0 1 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                              (0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0)
                             ) )
)

drDefineLineStyle(
;( DisplayName   LineStyle     Size    Pattern )
 ( display       solid          1      (1 1 1) )
 ( display       dashed         1      (1 1 1 1 0 0) )
 ( display       dots           1      (1 0 0) )
 ( display       dashDot        1      (1 1 1 0 0 1 0 0) )
 ( display       shortDash      1      (1 1 0 0) )
 ( display       doubleDash     1      (1 1 1 1 0 0 1 1 0 0) )
 ( display       hidden         1      (1 0 0 0) )
 ( display       thickLine      3      (1 1 1) )
 ( display       thickLine2     2      (1 1 1) )
 )

drDefinePacket(
;( DisplayName   PacketName                   Stipple    LineStyle    Fill      Outline )
 ( display       blacksolid_S                 solid      solid       black      black      )
 ( display       blue                         blank      solid       blue       blue       )
 ( display       blue_B                       blank      solid       blueB      blueB      )
 ( display       bluecross_S                  cross      solid       blue       blue       )
 ( display       blueX_S                      X          solid       blue       blue       )
 ( display       blueXthickLine               X          thickLine   blue       blue       )
 ( display       bluecrossthickLine           cross      thickLine   blue       blue       )
 ( display       bluedashed_L                 blank      dashed      blue       blue       )
 ( display       bluedashed_LB                blank      dashed      blueB      blueB      )
 ( display       bluedot1_S                   dot1       solid       blue       blue       )
 ( display       bluedot2_S                   dot2       solid       blue       blue       )
 ( display       bluedot3_S                   dot3       solid       blue       blue       )
 ( display       bluedot4_S                   dot4       solid       blue       blue       )
 ( display       bluesolid_S                  solid      solid       blue       blue       )
 ( display       bluethickLine2_L             blank      thickLine2  blue       blue       )
 ( display       bluebrickdashDot             brick      dashDot     blue       blue       )
 ( display       browndashed_L                blank      dashed      brown      brown      )
 ( display       browndashed_LB               blank      dashed      brownB     brownB     )
 ( display       brownbrickdashDot            brick      dashDot     brown      brown      )
 ( display       cream                        blank      solid       cream      cream      )
 ( display       creamXthickLine2             X          thickLine2  cream      cream      )
 ( display       creambrickdashDot            brick      dashDot     cream      cream      )
 ( display       creamcross_S                 cross      solid       cream      cream      )
 ( display       creamdashed_L                blank      dashed      cream      cream      )
 ( display       creamdot2_S                  dot2       solid       cream      cream      )
 ( display       creamdot4thickLine2          dot4       thickLine2  cream      cream      )
 ( display       creamshortDash_L             blank      shortDash   cream      cream      )
 ( display       creamslash_S                 slash      solid       cream      cream      )
 ( display       creamstipple0_S              stipple0   solid       cream      cream      )
 ( display       creamthickLine2_L            blank      thickLine2  cream      cream      )
 ( display       creamu_S                     u          solid       cream      cream      )
 ( display       creamud_S                    ud         solid       cream      cream      )
 ( display       creamutv_S                   utv        solid       cream      cream      )
 ( display       creamy5_S                    y5         solid       cream      cream      )
 ( display       creamy8_S                    y8         solid       cream      cream      )
 ( display       creamyd_S                    yd         solid       cream      cream      )
 ( display       creamz5_S                    z5         solid       cream      cream      )
 ( display       creamz8_S                    z8         solid       cream      cream      )
 ( display       creamzd_S                    zd         solid       cream      cream      )
 ( display       cyan                         blank      solid       cyan       cyan       )
 ( display       cyancross_S                  cross      solid       cyan       cyan       )
 ( display       cyanX_S                      X          solid       cyan       cyan       )
 ( display       cyanXthickLine2              X          thickLine2  cyan       cyan       )
 ( display       cyanbrickdashDot             brick      dashDot     cyan       cyan       )
 ( display       cyandashed_L                 blank      dashed      cyan       cyan       )
 ( display       cyandot1_S                   dot1       solid       cyan       cyan       )
 ( display       cyandot2_S                   dot2       solid       cyan       cyan       )
 ( display       cyandot3_S                   dot3       solid       cyan       cyan       )
 ( display       cyandot4_S                   dot4       solid       cyan       cyan       )
 ( display       cyandot4thickLine2           dot4       thickLine2  cyan       cyan       )
 ( display       cyansolid_S                  solid      solid       cyan       cyan       )
 ( display       cyanstipple0_S               stipple0   solid       cyan       cyan       )
 ( display       cyanstipple3_S               stipple3   solid       cyan       cyan       )
 ( display       cyanutv_S                    utv        solid       cyan       cyan       )
 ( display       cyanz9_S                     z9         solid       cyan       cyan       )
 ( display       forest                       blank      solid       forest     forest     )
 ( display       forestXthickLine             X          thickLine   forest     forest     )
 ( display       forestXthickLine2            X          thickLine2  forest     forest     )
 ( display       forestbrickdashDot           brick      dashDot     forest     forest     )
 ( display       forestdashed_L               blank      dashed      forest     forest     )
 ( display       forestdot1_S                 dot1       solid       forest     forest     )
 ( display       forestdot2_S                 dot2       solid       forest     forest     )
 ( display       forestdot3_S                 dot3       solid       forest     forest     )
 ( display       forestdot4_S                 dot4       solid       forest     forest     )
 ( display       forestdot4thickLine2         dot4       thickLine2  forest     forest     )
 ( display       foresthLine_S                hLine      solid       forest     forest     )
 ( display       foreststipple0_S             stipple0   solid       forest     forest     )
 ( display       forestu_S                    u          solid       forest     forest     )
 ( display       forestud_S                   ud         solid       forest     forest     )
 ( display       forestz10_S                  z10        solid       forest     forest     )
 ( display       forestz3_S                   z3         solid       forest     forest     )
 ( display       forestzd_S                   zd         solid       forest     forest     )
 ( display       forestx_S                    x          solid       forest     forest     )
 ( display       forestsolid_S                solid      solid       forest     forest     )
 ( display       gold                         blank      solid       gold       gold       )
 ( display       gold_B                       blank      solid       goldB      goldB      )
 ( display       goldcross_S                  cross      solid       gold       gold       )
 ( display       goldXthickLine2              X          thickLine2  gold       gold       )
 ( display       goldbrickdashDot             brick      dashDot     gold       gold       )
 ( display       golddashed_L                 blank      dashed      gold       gold       )
 ( display       golddashed_LB                blank      dashed      goldB      goldB      )
 ( display       golddot1_S                   dot1       solid       gold       gold       )
 ( display       golddot2_S                   dot2       solid       gold       gold       )
 ( display       golddot3_S                   dot3       solid       gold       gold       )
 ( display       golddot4_S                   dot4       solid       gold       gold       )
 ( display       golddots_S                   dots       solid       gold       gold       )
 ( display       golddot4thickLine2           dot4       thickLine2  gold       gold       )
 ( display       goldstipple0_S               stipple0   solid       gold       gold       )
 ( display       goldstipple3_S               stipple3   solid       gold       gold       )
 ( display       goldutv_S                    utv        solid       gold       gold       )
 ( display       goldx_S                      x          solid       gold       gold       )
 ( display       goldy6_S                     y6         solid       gold       gold       )
 ( display       goldy8_S                     y8         solid       gold       gold       )
 ( display       goldz6_S                     z6         solid       gold       gold       )
 ( display       goldz8_S                     z8         solid       gold       gold       )
 ( display       goldsolid_S                  solid      solid       gold       gold       )
 ( display       gray                         blank      solid       gray       gray       )
 ( display       graydot1thickLine            dot1       thickLine   gray       gray       )
 ( display       graydot1_S                   dot1       solid       gray       gray       )
 ( display       graydot2_S                   dot2       solid       gray       gray       )
 ( display       graydot3_S                   dot3       solid       gray       gray       )
 ( display       graydot4_S                   dot4       solid       gray       gray       )
 ( display       graydots_S                   dots       solid       gray       gray       )
 ( display       grayshortDash_L              blank      shortDash   gray       gray       )
 ( display       graysolid_S                  solid      solid       gray       gray       )
 ( display       graythickLine2_L             blank      thickLine2  gray       gray       )
 ( display       graythickLine_L              blank      thickLine   gray       gray       )
 ( display       green                        blank      solid       green      green      )
 ( display       green_B                      blank      solid       greenB     greenB     )
 ( display       greenX_S                     X          solid       green      green      )
 ( display       greenXthickLine2             X          thickLine2  green      green      )
 ( display       greencross_S                 cross      solid       green      green      )
 ( display       greendashed_L                blank      dashed      green      green      )
 ( display       greendashed_LB               blank      dashed      greenB     greenB     )
 ( display       greendot1_S                  dot1       solid       green      green      )
 ( display       greendot2_S                  dot2       solid       green      green      )
 ( display       greendot3_S                  dot3       solid       green      green      )
 ( display       greendot4_S                  dot4       solid       green      green      )
 ( display       greendots_S                  dots       solid       green      green      )
 ( display       greensolid_S                 solid      solid       green      green      )
 ( display       greenstipple0_S              stipple0   solid       green      green      )
 ( display       greenstipple3_S              stipple3   solid       green      green      )
 ( display       greenthickLine2_L            blank      thickLine2  green      green      )
 ( display       greentriangle_S              triangle   solid       green      green      )
 ( display       joy1                         blank      solid       joy1       joy1       )
 ( display       lime                         blank      solid       lime       lime       )
 ( display       limeX_S                      X          solid       lime       lime       )
 ( display       limeXthickLine2              X          thickLine2  lime       lime       )
 ( display       limebrickdashDot             brick      dashDot     lime       lime       )
 ( display       limedashed_L                 blank      dashed      lime       lime       )
 ( display       limedot1_S                   dot1       solid       lime       lime       )
 ( display       limedot2_S                   dot2       solid       lime       lime       )
 ( display       limedot3_S                   dot3       solid       lime       lime       )
 ( display       limedot4_S                   dot4       solid       lime       lime       )
 ( display       limedots_S                   dots       solid       lime       lime       )
 ( display       limedot4thickLine2           dot4       thickLine2  lime       lime       )
 ( display       limehLine_S                  hLine      solid       lime       lime       )
 ( display       limesolid_S                  solid      solid       lime       lime       )
 ( display       limestipple0_S               stipple0   solid       lime       lime       )
 ( display       limestipple3_S               stipple3   solid       lime       lime       )
 ( display       limeu_S                      u          solid       lime       lime       )
 ( display       limeud_S                     ud         solid       lime       lime       )
 ( display       limey7_S                     y7         solid       lime       lime       )
 ( display       limey9_S                     y9         solid       lime       lime       )
 ( display       limeyd_S                     yd         solid       lime       lime       )
 ( display       limez7_S                     z7         solid       lime       lime       )
 ( display       limez9_S                     z9         solid       lime       lime       )
 ( display       limezd_S                     zd         solid       lime       lime       )
 ( display       magenta                      blank      solid       magenta    magenta    )
 ( display       magentaX_S                   X          solid       magenta    magenta    )
 ( display       magentadot1_S                dot1       solid       magenta    magenta    )
 ( display       magentadot2_S                dot2       solid       magenta    magenta    )
 ( display       magentadot3_S                dot3       solid       magenta    magenta    )
 ( display       magentadot4_S                dot4       solid       magenta    magenta    )
 ( display       magentadots_S                dots       solid       magenta    magenta    )
 ( display       magentahLine_S               hLine      solid       magenta    magenta    )
 ( display       magentashortDash_L           blank      shortDash   magenta    magenta    )
 ( display       magentasolid_S               solid      solid       magenta    magenta    )
 ( display       magentathickLine2_L          blank      thickLine2  magenta    magenta    )
 ( display       magentathickLine_L           blank      thickLine   magenta    magenta    )
 ( display       magentatriangle_S            triangle   solid       magenta    magenta    )
 ( display       navy                         blank      solid       navy       navy       )
 ( display       navysolid_S                  solid      solid       navy       navy       )
 ( display       orange                       blank      solid       orange     orange     )
 ( display       orange_B                     blank      solid       orangeB    orangeB    )
 ( display       orangeX_S                    X          solid       orange     orange     )
 ( display       orangeXthickLine             X          thickLine   orange     orange     )
 ( display       orangeXthickLine2            X          thickLine2  orange     orange     )
 ( display       orangecrossdashed            cross      dashed      orange     orange     )
 ( display       orangecrossthickLine         cross      thickLine   orange     orange     )
 ( display       orangebrickdashDot           brick      dashDot     orange     orange     )
 ( display       orangedagger_S               dagger     solid       orange     orange     )
 ( display       orangedashed_L               blank      dashed      orange     orange     )
 ( display       orangedashed_LB              blank      dashed      orangeB    orangeB    )
 ( display       orangedots_S                 dots       solid       orange     orange     )
 ( display       orangesolid_S                solid      solid       orange     orange     )
 ( display       orangethickLine2_L           blank      thickLine2  orange     orange     )
 ( display       orangetriangle_S             triangle   solid       orange     orange     )
 ( display       pink                         blank      solid       pink       pink       )
 ( display       pink_B                       blank      solid       pinkB      pinkB      )
 ( display       pinkbackSlash_S              backSlash  solid       pink       pink       )
 ( display       pinkbrickdashDot             brick      dashDot     pink       pink       )
 ( display       pinkdaggerdashed             dagger     dashed      pink       pink       )
 ( display       pinkdashed_L                 blank      dashed      pink       pink       )
 ( display       pinkdashed_LB                blank      dashed      pinkB      pinkB      )
 ( display       pinkdots_S                   dots       solid       pink     	pink       )
 ( display       pinksolid_S                  solid      solid       pink       pink       )
 ( display       pinktriangle_S               triangle   solid       pink       pink       )
 ( display       purple                       blank      solid       purple     purple     )
 ( display       purple_B                     blank      solid       purpleB    purpleB    )
 ( display       purplebackSlash_S            backSlash  solid       purple     purple     )
 ( display       purpledashed_L               blank      dashed      purple     purple     )
 ( display       purpledashed_LB              blank      dashed      purpleB    purpleB    )
 ( display       purpleslash_S                slash      solid       purple     purple     )
 ( display       purpledot2_S                 dot2       solid       purple     purple     )
 ( display       purpledot3_S                 dot3       solid       purple     purple     )
 ( display       purplesolid_S                solid      solid       purple     purple     )
 ( display       purpletriangle_S             triangle   solid       purple     purple     )
 ( display       purpleutv_S                  utv        solid       purple     purple     )
 ( display       purplevLine_S                vLine      solid       purple     purple     )
 ( display       purpley3_S                   y3         solid       purple     purple     )
 ( display       purplez3_S                   z3         solid       purple     purple     )
 ( display       red                          blank      solid       red        red        )
 ( display       red_B                        blank      solid       redB       redB       )
 ( display       redXthickLine                X          thickLine   red        red        )
 ( display       redXthickLine2               X          thickLine2  red        red        )
 ( display       redbackSlash_S               backSlash  solid       red        red        )
 ( display       reddagger_S                  dagger     solid       red        red        )
 ( display       reddashed_L                  blank      dashed      red        red        )
 ( display       reddashed_LB                 blank      dashed      redB       redB       )
 ( display       reddot2_S                    dot2       solid       red        red        )
 ( display       reddot3_S                    dot3       solid       red        red        )
 ( display       reddots_S                    dots       solid       red      	red        )
 ( display       whitehidden_L                blank      hidden      white      white      )
 ( display       yellowhidden_L               blank      hidden      yellow     yellow     )
 ( display       redhidden_L                  blank      hidden      red        red        )
 ( display       redsolid_S                   solid      solid       red        red        )
 ( display       redtriangle_S                triangle   solid       red        red        )
 ( display       redthickLine2_L              blank      thickLine2  red        red        )
 ( display       redthickLine_L               blank      thickLine   red        red        )
 ( display       silver                       blank      solid       silver     silver     )
 ( display       silver_B                     blank      solid       silverB    silverB    )
 ( display       silverbackSlash_S            backSlash  solid       silver     silver     )
 ( display       silvercrossthickLine         cross      thickLine   silver     silver     )
 ( display       silverdashed_L               blank      dashed      silver     silver     )
 ( display       silverdashed_LB              blank      dashed      silverB    silverB    )
 ( display       silversolid_S                solid      solid       silver     silver     )
 ( display       silverslash_S                slash      solid       silver     silver     )
 ( display       silverstipple0_S             stipple0   solid       silver     silver     )
 ( display       silverstipple1_S             stipple1   solid       silver     silver     )
 ( display       silverstipple2_S             stipple2   solid       silver     silver     )
 ( display       silverstipple3_S             stipple3   solid       silver     silver     )
 ( display       silverdotsthickLine2         dots      thickLine2   silver     silver     )
 ( display       slateXthickLine2             X          thickLine2  slate      slate      )
 ( display       slatebrickdashDot            brick      dashDot     slate      slate      )
 ( display       slatedashed_L                blank      dashed      slate      slate      )
 ( display       slatedot3_S                  dot3       solid       slate      slate      )
 ( display       slatedot4_S                  dot4       solid       slate      slate      )
 ( display       slatedot4thickLine2          dot4       thickLine2  slate      slate      )
 ( display       slateu_S                     u          solid       slate      slate      )
 ( display       slateud_S                    ud         solid       slate      slate      )
 ( display       slatey4_S                    y4         solid       slate      slate      )
 ( display       slateyd_S                    yd         solid       slate      slate      )
 ( display       slatez4_S                    z4         solid       slate      slate      )
 ( display       slatezd_S                    zd         solid       slate      slate      )
 ( display       slatesolid_S                 solid      solid       slate      slate      )
 ( display       tan                          blank      solid       tan        tan        )
 ( display       tanXthickLine2               X          thickLine2  tan        tan        )
 ( display       tanbrickdashDot              brick      dashDot     tan        tan        )
 ( display       tancrossthickLine            cross      thickLine   tan        tan        )
 ( display       tandashed_L                  blank      dashed      tan        tan        )
 ( display       tandot1_S                    dot1       solid       tan        tan        )
 ( display       tandot2_S                    dot2       solid       tan        tan        )
 ( display       tandot3_S                    dot3       solid       tan        tan        )
 ( display       tandot4_S                    dot4       solid       tan        tan        )
 ( display       tandot4thickLine2            dot4       thickLine2  tan        tan        )
 ( display       tanshortDash_L               blank      shortDash   tan        tan        )
 ( display       tansolid_S                   solid      solid       tan        tan        )
 ( display       tanstipple3_S                stipple3   solid       tan        tan        )
 ( display       tanthickLine2_L              blank      thickLine2  tan        tan        )
 ( display       tanu_S                       u          solid       tan        tan        )
 ( display       tanud_S                      ud         solid       tan        tan        )
 ( display       tanutv_S                     utv        solid       tan        tan        )
 ( display       tanvLine_S                   vLine      solid       tan        tan        )
 ( display       tany6_S                      y6         solid       tan        tan        )
 ( display       tany7_S                      y7         solid       tan        tan        )
 ( display       tanyd_S                      yd         solid       tan        tan        )
 ( display       tanz6_S                      z6         solid       tan        tan        )
 ( display       tanz7_S                      z7         solid       tan        tan        )
 ( display       tanzd_S                      zd         solid       tan        tan        )
 ( display       violet                       blank      solid       violet     violet     )
 ( display       violetdot1_S                 dot1       solid       violet     violet     )
 ( display       violetdot2_S                 dot2       solid       violet     violet     )
 ( display       violetdot4_S                 dot4       solid       violet     violet     )
 ( display       violetstipple0_S             stipple0   solid       violet     violet     )
 ( display       violetstipple1_S             stipple1   solid       violet     violet     )
 ( display       violetstipple2_S             stipple2   solid       violet     violet     )
 ( display       violetstipple3_S             stipple3   solid       violet     violet     )
 ( display       white                        blank      solid       white      white      )
 ( display       whiteX_S                     X          solid       white      white      )
 ( display       whiteX_SB                    X          solid       white      whiteB     )
 ( display       whitecrossdashed             cross      dashed      white      white      )
 ( display       whitedashed_L                blank      dashed      white      white      )
 ( display       whitedot4_S                  dot4       solid       white      white      )
 ( display       whiteslash_S                 slash      solid       white      white      )
 ( display       whitesolid_S                 solid      solid       white      white      )
 ( display       whitetriangle_S              triangle   solid       white      white      )
 ( display       whiteutv_S                   utv        solid       white      white      )
 ( display       whitey4_S                    y4         solid       white      white      )
 ( display       whitez4_S                    z4         solid       white      white      )
 ( display       winColor2winColor1crossthickLine   cross      thickLine   winColor2  winColor1  )
 ( display       winColor2winColor1daggerthickLine   dagger     thickLine   winColor2  winColor1  )
 ( display       winColor2winColor1dot3thickLine   dot3       thickLine   winColor2  winColor1  )
 ( display       winColor3XthickLine2         X          thickLine2  winColor3  winColor3  )
 ( display       winColor3brickdashDot        brick      dashDot     winColor3  winColor3  )
 ( display       winColor3dashed_L            blank      dashed      winColor3  winColor3  )
 ( display       winColor3dot3_S              dot3       solid       winColor3  winColor3  )
 ( display       winColor3dot4thickLine2      dot4       thickLine2  winColor3  winColor3  )
 ( display       winColor3stipple3_S          stipple3   solid       winColor3  winColor3  )
 ( display       winColor3u_S                 u          solid       winColor3  winColor3  )
 ( display       winColor3ud_S                ud         solid       winColor3  winColor3  )
 ( display       winColor3y5_S                y5         solid       winColor3  winColor3  )
 ( display       winColor3yd_S                yd         solid       winColor3  winColor3  )
 ( display       winColor3z5_S                z5         solid       winColor3  winColor3  )
 ( display       winColor3zd_S                zd         solid       winColor3  winColor3  )
 ( display       winColor3solid_S             solid      solid       winColor3  winColor3  )
 ( display       winColor4brickdashDot        brick      dashDot     winColor4  winColor4  )
 ( display       winColor5                    blank      solid       winColor5  winColor5  )
 ( display       winColor5_B                  blank      solid       winColor5B winColor5B )
 ( display       winColor5brickdashDot        brick      dashDot     winColor5  winColor5  )
 ( display       winColor5dashed_L            blank      dashed      winColor5  winColor5  )
 ( display       winColor5dashed_LB           blank      dashed      winColor5B winColor5B )
 ( display       yellow                       blank      solid       yellow     yellow     )
 ( display       yellowX_S                    X          solid       yellow     yellow     )
 ( display       yellowX_SB                   X          solid       yellow     yellowB    )
 ( display       yellowcrossthickLine         cross      thickLine   yellow     yellow     )
 ( display       yellowdot1_S                 dot1       solid       yellow     yellow     )
 ( display       yellowdot2_S                 dot2       solid       yellow     yellow     )
 ( display       yellowdot3_S                 dot3       solid       yellow     yellow     )
 ( display       yellowdot4_S                 dot4       solid       yellow     yellow     )
 ( display       yellowdots_S                 dots       solid       yellow     yellow     )
 ( display       yellowhLine_S                hLine      solid       yellow     yellow     )
 ( display       yellowshortDash_L            blank      shortDash   yellow     yellow     )
 ( display       yellowsolid_S                solid      solid       yellow     yellow     )
 ( display       yellowz2_S                   z2         solid       yellow     yellow     )
 ( display       blacksolid_S                 solid      solid       black      black      )
 ( display       blue                         blank      solid       blue       blue       )
 ( display       blueX_S                      X          solid       blue       blue       )
 ( display       blueXthickLine               X          thickLine   blue       blue       )
 ( display       bluecrossthickLine           cross      thickLine   blue       blue       )
 ( display       bluedashed_L                 blank      dashed      blue       blue       )
 ( display       bluedashed_LB                blank      dashed      blueB      blueB      )
 ( display       bluedot1_S                   dot1       solid       blue       blue       )
 ( display       bluesolid_S                  solid      solid       blue       blue       )
 ( display       bluethickLine2_L             blank      thickLine2  blue       blue       )
 ( display       brown                        blank      solid       brown      brown      )
 ( display       brown_B                      blank      solid       brownB     brownB     )
 ( display       browndashed_L                blank      dashed      brown      brown      )
 ( display       browndashed_LB               blank      dashed      brownB     brownB     )
 ( display       cream                        blank      solid       cream      cream      )
 ( display       creamXthickLine2             X          thickLine2  cream      cream      )
 ( display       creamcross_S                 cross      solid       cream      cream      )
 ( display       creamdot3dashed              dot3       dashed      cream      cream      )
 ( display       creamdot3shortDash           dot3       shortDash   cream      cream      )
 ( display       creamshortDash_L             blank      shortDash   cream      cream      )
 ( display       creamslash_S                 slash      solid       cream      cream      )
 ( display       creamthickLine2_L            blank      thickLine2  cream      cream      )
 ( display       cyan                         blank      solid       cyan       cyan       )
 ( display       cyanX_S                      X          solid       cyan       cyan       )
 ( display       cyanXthickLine2              X          thickLine2  cyan       cyan       )
 ( display       cyandot3dashed               dot3       dashed      cyan       cyan       )
 ( display       cyandot3thickLine            dot3       thickLine   cyan       cyan       )
 ( display       cyandot4_S                   dot4       solid       cyan       cyan       )
 ( display       cyansolid_S                  solid      solid       cyan       cyan       )
 ( display       forest                       blank      solid       forest     forest     )
 ( display       forestXthickLine2            X          thickLine2  forest     forest     )
 ( display       forestdot3dashed             dot3       dashed      forest     forest     )
 ( display       forestdot4_S                 dot4       solid       forest     forest     )
 ( display       forestx_S                    x          solid       forest     forest     )
 ( display       gold                         blank      solid       gold       gold       )
 ( display       goldXthickLine2              X          thickLine2  gold       gold       )
 ( display       golddashed_L                 blank      dashed      gold       gold       )
 ( display       golddashed_LB                blank      dashed      goldB      goldB      )
 ( display       golddot3dashed               dot3       dashed      gold       gold       )
 ( display       goldstipple0_S               stipple0   solid       gold       gold       )
 ( display       goldstipple3_S               stipple3   solid       gold       gold       )
 ( display       goldx_S                      x          solid       gold       gold       )
 ( display       gray                         blank      solid       gray       gray       )
 ( display       graydot1thickLine            dot1       thickLine   gray       gray       )
 ( display       graydot3thickLine2           dot3       thickLine2  gray       gray       )
 ( display       graydots_S                   dots       solid       gray       gray       )
 ( display       grayshortDash_L              blank      shortDash   gray       gray       )
 ( display       graysolid_S                  solid      solid       gray       gray       )
 ( display       graythickLine2_L             blank      thickLine2  gray       gray       )
 ( display       green                        blank      solid       green      green      )
 ( display       greenX_S                     X          solid       green      green      )
 ( display       greencross_S                 cross      solid       green      green      )
 ( display       greendashed_L                blank      dashed      green      green      )
 ( display       greendashed_LB               blank      dashed      greenB     greenB     )
 ( display       greendot3thickLine           dot3       thickLine   green      green      )
 ( display       greendot4_S                  dot4       solid       green      green      )
 ( display       greendots_S                  dots       solid       green      green      )
 ( display       greensolid_S                 solid      solid       green      green      )
 ( display       greenthickLine2_L            blank      thickLine2  green      green      )
 ( display       greentriangle_S              triangle   solid       green      green      )
 ( display       joy1                         blank      solid       joy1       joy1       )
 ( display       joy1brickdashDot             brick      dashDot     joy1       joy1       )
 ( display       lime                         blank      solid       lime       lime       )
 ( display       limeX_S                      X          solid       lime       lime       )
 ( display       limeXthickLine2              X          thickLine2  lime       lime       )
 ( display       limebrickthickLine           brick      thickLine   lime       lime       )
 ( display       limecrossthickLine           cross      thickLine   lime       lime       )
 ( display       limedot3_S                   dot3       solid       lime       lime       )
 ( display       limedot3dashed               dot3       dashed      lime       lime       )
 ( display       limedot3thickLine            dot3       thickLine   lime       lime       )
 ( display       limedot4_S                   dot4       solid       lime       lime       )
 ( display       limehLine_S                  hLine      solid       lime       lime       )
 ( display       limesolid_S                  solid      solid       lime       lime       )
 ( display       magenta                      blank      solid       magenta    magenta    )
 ( display       magentaX_S                   X          solid       magenta    magenta    )
 ( display       magentadot2_S                dot2       solid       magenta    magenta    )
 ( display       magentadot3thickLine         dot3       thickLine   magenta    magenta    )
 ( display       magentadots_S                dots       solid       magenta    magenta    )
 ( display       magentashortDash_L           blank      shortDash   magenta    magenta    )
 ( display       magentasolid_S               solid      solid       magenta    magenta    )
 ( display       magentastipple0thickLine     stipple0   thickLine   magenta    magenta    )
 ( display       magentathickLine2_L          blank      thickLine2  magenta    magenta    )
 ( display       magentathickLine_L           blank      thickLine   magenta    magenta    )
 ( display       nwdummy_d1                   solid      solid       yellow     yellow     )
 ( display       rpdummy_d1                   blank      shortDash   yellow     yellow     )
 ( display       navy                         blank      solid       navy       navy       )
 ( display       navysolid_S                  solid      solid       navy       navy       )
 ( display       orange                       blank      solid       orange     orange     )
 ( display       orangeX_S                    X          solid       orange     orange     )
 ( display       orangedashed_L               blank      dashed      orange     orange     )
 ( display       orangedashed_LB              blank      dashed      orangeB    orangeB    )
 ( display       orangedots_S                 dots       solid       orange     orange     )
 ( display       orangesolid_S                solid      solid       orange     orange     )
 ( display       orangestipple0_S             stipple0   solid       orange     orange     )
 ( display       orangethickLine2_L           blank      thickLine2  orange     orange     )
 ( display       pink                         blank      solid       pink       pink       )
 ( display       pinkbrick_SN                 brick      _NA_        pink       pink       )
 ( display       pinkbrickdots                brick      dots        pink       pink       )
 ( display       pinkdaggerdashed             dagger     dashed      pink       pink       )
 ( display       pinkdashed_L                 blank      dashed      pink       pink       )
 ( display       pinkdashed_LB                blank      dashed      pinkB      pinkB      )
 ( display       pinksolid_S                  solid      solid       pink       pink       )
 ( display       purple                       blank      solid       purple     purple     )
 ( display       purpledashed_L               blank      dashed      purple     purple     )
 ( display       purpledashed_LB              blank      dashed      purpleB    purpleB    )
 ( display       purpledot2_S                 dot2       solid       purple     purple     )
 ( display       purplesolid_S                solid      solid       purple     purple     )
 ( display       purpletriangle_S             triangle   solid       purple     purple     )
 ( display       purplevLine_S                vLine      solid       purple     purple     )
 ( display       red                          blank      solid       red        red        )
 ( display       redXthickLine                X          thickLine   red        red        )
 ( display       redXthickLine2               X          thickLine2  red        red        )
 ( display       redbackSlash_S               backSlash  solid       red        red        )
 ( display       reddagger_S                  dagger     solid       red        red        )
 ( display       reddashed_L                  blank      dashed      red        red        )
 ( display       reddashed_LB                 blank      dashed      redB       redB       )
 ( display       reddot3_S                    dot3       solid       red        red        )
 ( display       reddot3thickLine             dot3       thickLine   red        red        )
 ( display       redhidden_L                  blank      hidden      red        red        )
 ( display       redsolid_S                   solid      solid       red        red        )
 ( display       redthickLine2_L              blank      thickLine2  red        red        )
 ( display       redthickLine_L               blank      thickLine   red        red        )
 ( display       silver                       blank      solid       silver     silver     )
 ( display       silverdashed_L               blank      dashed      silver     silver     )
 ( display       silverdashed_LB              blank      dashed      silverB    silverB    )
 ( display       silversolid_S                solid      solid       silver     silver     )
 ( display       slateXthickLine2             X          thickLine2  slate      slate      )
 ( display       slatedot3dashed              dot3       dashed      slate      slate      )
 ( display       slatedot4_S                  dot4       solid       slate      slate      )
 ( display       slatehLinethickLine2         hLine      thickLine2  slate      slate      )
 ( display       slatevLinethickLine2         vLine      thickLine2  slate      slate      )
 ( display       tan                          blank      solid       tan        tan        )
 ( display       tanXthickLine2               X          thickLine2  tan        tan        )
 ( display       tancrossthickLine            cross      thickLine   tan        tan        )
 ( display       tandot3dashed                dot3       dashed      tan        tan        )
 ( display       tanshortDash_L               blank      shortDash   tan        tan        )
 ( display       tansolid_S                   solid      solid       tan        tan        )
 ( display       tanstipple3_S                stipple3   solid       tan        tan        )
 ( display       tanthickLine2_L              blank      thickLine2  tan        tan        )
 ( display       tanvLine_S                   vLine      solid       tan        tan        )
 ( display       violet                       blank      solid       violet     violet     )
 ( display       white                        blank      solid       white      white      )
 ( display       whiteX_S                     X          solid       white      white      )
 ( display       whiteX_SB                    X          solid       white      whiteB     )
 ( display       whitecrossdashed             cross      dashed      white      white      )
 ( display       whitedaggerdashed            dagger     dashed      white      white      )
 ( display       whitedashed_L                blank      dashed      white      white      )
 ( display       whitedot4_S                  dot4       solid       white      white      )
 ( display       whiteslash_S                 slash      solid       white      white      )
 ( display       whitesolid_S                 solid      solid       white      white      )
 ( display       whitetriangle_S              triangle   solid       white      white      )
 ( display       whitex_S                     x          solid       white      white      )
 ( display       winColor2winColor1crossthickLine   cross      thickLine   winColor2  winColor1  )
 ( display       winColor2winColor1daggerthickLine   dagger     thickLine   winColor2  winColor1  )
 ( display       winColor3XthickLine2         X          thickLine2  winColor3  winColor3  )
 ( display       winColor3dot3_S              dot3       solid       winColor3  winColor3  )
 ( display       winColor3dot3dashed          dot3       dashed      winColor3  winColor3  )
 ( display       winColor4brickdashDot        brick      dashDot     winColor4  winColor4  )
 ( display       winColor5dashed_L            blank      dashed      winColor5  winColor5  )
 ( display       winColor5dashed_LB           blank      dashed      winColor5B winColor5B )
 ( display       winColor5dot3dashed          dot3       dashed      winColor5  winColor5  )
 ( display       yellow                       blank      solid       yellow     yellow     )
 ( display       yellowX_S                    X          solid       yellow     yellow     )
 ( display       yellowX_SB                   X          solid       yellow     yellowB    )
 ( display       yellowcrossthickLine         cross      thickLine   yellow     yellow     )
 ( display       yellowdaggerthickLine        dagger     thickLine   yellow     yellow     )
 ( display       yellowdot3thickLine          dot3       thickLine   yellow     yellow     )
 ( display       yellowdot4_S                 dot4       solid       yellow     yellow     )
 ( display       yellowhLine_S                hLine      solid       yellow     yellow     )
 ( display       yellowshortDash_L            blank      shortDash   yellow     yellow     )
 ( display       yellowsolid_S                solid      solid       yellow     yellow     )
 ( display       blacksolid_S                 solid      solid       black      black      )
 ( display       blue                         blank      solid       blue       blue       )
 ( display       blueX_S                      X          solid       blue       blue       )
 ( display       blueXthickLine               X          thickLine   blue       blue       )
 ( display       bluecrossthickLine           cross      thickLine   blue       blue       )
 ( display       bluedashed_L                 blank      dashed      blue       blue       )
 ( display       bluedashed_LB                blank      dashed      blueB      blueB      )
 ( display       bluedot1_S                   dot1       solid       blue       blue       )
 ( display       bluesolid_S                  solid      solid       blue       blue       )
 ( display       bluethickLine2_L             blank      thickLine2  blue       blue       )
 ( display       browndashed_L                blank      dashed      brown      brown      )
 ( display       browndashed_LB               blank      dashed      brownB     brownB     )
 ( display       cream                        blank      solid       cream      cream      )
 ( display       creamXthickLine2             X          thickLine2  cream      cream      )
 ( display       creamcross_S                 cross      solid       cream      cream      )
 ( display       creamdot1_S                  dot1       solid       cream      cream      )
 ( display       creamdot2_S                  dot2       solid       cream      cream      )
 ( display       creamdot3dashed              dot3       dashed      cream      cream      )
 ( display       creamslash_S                 slash      solid       cream      cream      )
 ( display       creamthickLine2_L            blank      thickLine2  cream      cream      )
 ( display       cyan                         blank      solid       cyan       cyan       )
 ( display       cyanX_S                      X          solid       cyan       cyan       )
 ( display       cyanXthickLine               X          thickLine   cyan       cyan       )
 ( display       cyanXthickLine2              X          thickLine2  cyan       cyan       )
 ( display       cyandot3dashed               dot3       dashed      cyan       cyan       )
 ( display       cyandot4_S                   dot4       solid       cyan       cyan       )
 ( display       cyansolid_S                  solid      solid       cyan       cyan       )
 ( display       forest                       blank      solid       forest     forest     )
 ( display       forestXthickLine2            X          thickLine2  forest     forest     )
 ( display       forestdot3dashed             dot3       dashed      forest     forest     )
 ( display       forestdot4_S                 dot4       solid       forest     forest     )
 ( display       foreststipple0_S             stipple0   solid       forest     forest     )
 ( display       forestx_S                    x          solid       forest     forest     )
 ( display       gold                         blank      solid       gold       gold       )
 ( display       goldXthickLine2              X          thickLine2  gold       gold       )
 ( display       golddashed_L                 blank      dashed      gold       gold       )
 ( display       golddashed_LB                blank      dashed      goldB      goldB      )
 ( display       golddot3dashed               dot3       dashed      gold       gold       )
 ( display       golddot4_S                   dot4       solid       gold       gold       )
 ( display       goldstipple0_S               stipple0   solid       gold       gold       )
 ( display       goldstipple3_S               stipple3   solid       gold       gold       )
 ( display       goldx_S                      x          solid       gold       gold       )
 ( display       gray                         blank      solid       gray       gray       )
 ( display       graydot1thickLine            dot1       thickLine   gray       gray       )
 ( display       graydots_S                   dots       solid       gray       gray       )
 ( display       grayshortDash_L              blank      shortDash   gray       gray       )
 ( display       graysolid_S                  solid      solid       gray       gray       )
 ( display       graythickLine2_L             blank      thickLine2  gray       gray       )
 ( display       green                        blank      solid       green      green      )
 ( display       greenX_S                     X          solid       green      green      )
 ( display       greencross_S                 cross      solid       green      green      )
 ( display       greendashed_L                blank      dashed      green      green      )
 ( display       greendashed_LB               blank      dashed      greenB     greenB     )
 ( display       greendot4_S                  dot4       solid       green      green      )
 ( display       greendots_S                  dots       solid       green      green      )
 ( display       greensolid_S                 solid      solid       green      green      )
 ( display       greenthickLine2_L            blank      thickLine2  green      green      )
 ( display       greentriangle_S              triangle   solid       green      green      )
 ( display       joy1                         blank      solid       joy1       joy1       )
 ( display       joy1brickdashDot             brick      dashDot     joy1       joy1       )
 ( display       lime                         blank      solid       lime       lime       )
 ( display       limeX_S                      X          solid       lime       lime       )
 ( display       limeXthickLine2              X          thickLine2  lime       lime       )
 ( display       limedot3dashed               dot3       dashed      lime       lime       )
 ( display       limedot4_S                   dot4       solid       lime       lime       )
 ( display       limedots_S                   dots       solid       lime       lime       )
 ( display       limehLine_S                  hLine      solid       lime       lime       )
 ( display       limesolid_S                  solid      solid       lime       lime       )
 ( display       magenta                      blank      solid       magenta    magenta    )
 ( display       magentaX_S                   X          solid       magenta    magenta    )
 ( display       magentadot2_S                dot2       solid       magenta    magenta    )
 ( display       magentadots_S                dots       solid       magenta    magenta    )
 ( display       magentashortDash_L           blank      shortDash   magenta    magenta    )
 ( display       magentasolid_S               solid      solid       magenta    magenta    )
 ( display       magentathickLine_L           blank      thickLine   magenta    magenta    )
 ( display       navy                         blank      solid       navy       navy       )
 ( display       navysolid_S                  solid      solid       navy       navy       )
 ( display       orange                       blank      solid       orange     orange     )
 ( display       orangeX_S                    X          solid       orange     orange     )
 ( display       orangecrossdashed            cross      dashed      orange     orange     )
 ( display       orangedashed_L               blank      dashed      orange     orange     )
 ( display       orangedashed_LB              blank      dashed      orangeB    orangeB    )
 ( display       orangedot2_S                 dot2       solid       orange     orange     )
 ( display       orangedots_S                 dots       solid       orange     orange     )
 ( display       orangesolid_S                solid      solid       orange     orange     )
 ( display       orangethickLine2_L           blank      thickLine2  orange     orange     )
 ( display       pink                         blank      solid       pink       pink       )
 ( display       pinkbrickdashDot             brick      dashDot     pink       pink       )
 ( display       pinkdaggerdashed             dagger     dashed      pink       pink       )
 ( display       pinkdashed_L                 blank      dashed      pink       pink       )
 ( display       pinkdashed_LB                blank      dashed      pinkB      pinkB      )
 ( display       pinkdots_S                   dots       solid       pink       pink       )
 ( display       pinksolid_S                  solid      solid       pink       pink       )
 ( display       purple                       blank      solid       purple     purple     )
 ( display       purpleXthickLine2            X          thickLine2  purple     purple     )
 ( display       purpledashed_L               blank      dashed      purple     purple     )
 ( display       purpledashed_LB              blank      dashed      purpleB    purpleB    )
 ( display       purpleslash_S                slash      solid       purple     purple     )
 ( display       purplesolid_S                solid      solid       purple     purple     )
 ( display       purpletriangle_S             triangle   solid       purple     purple     )
 ( display       purplevLine_S                vLine      solid       purple     purple     )
 ( display       red                          blank      solid       red        red        )
 ( display       redXthickLine                X          thickLine   red        red        )
 ( display       redXthickLine2               X          thickLine2  red        red        )
 ( display       redbackSlash_S               backSlash  solid       red        red        )
 ( display       reddagger_S                  dagger     solid       red        red        )
 ( display       reddashed_L                  blank      dashed      red        red        )
 ( display       reddashed_LB                 blank      dashed      redB       redB       )
 ( display       reddot2_S                    dot2       solid       red        red        )
 ( display       reddot3_S                    dot3       solid       red        red        )
 ( display       reddots_S                    dots       solid       red        red        )
 ( display       redhidden_L                  blank      hidden      red        red        )
 ( display       redsolid_S                   solid      solid       red        red        )
 ( display       redthickLine2_L              blank      thickLine2  red        red        )
 ( display       redthickLine_L               blank      thickLine   red        red        )
 ( display       silver                       blank      solid       silver     silver     )
 ( display       silverdashed_L               blank      dashed      silver     silver     )
 ( display       silverdashed_LB              blank      dashed      silverB    silverB    )
 ( display       silversolid_S                solid      solid       silver     silver     )
 ( display       slateXthickLine2             X          thickLine2  slate      slate      )
 ( display       slatedot3_S                  dot3       solid       slate      slate      )
 ( display       slatedot3dashed              dot3       dashed      slate      slate      )
 ( display       slatedot4_S                  dot4       solid       slate      slate      )
 ( display       slatehLinethickLine2         hLine      thickLine2  slate      slate      )
 ( display       slatevLinethickLine2         vLine      thickLine2  slate      slate      )
 ( display       tan                          blank      solid       tan        tan        )
 ( display       tanXthickLine2               X          thickLine2  tan        tan        )
 ( display       tancrossthickLine            cross      thickLine   tan        tan        )
 ( display       tandot3dashed                dot3       dashed      tan        tan        )
 ( display       tandot4_S                    dot4       solid       tan        tan        )
 ( display       tansolid_S                   solid      solid       tan        tan        )
 ( display       tanstipple3_S                stipple3   solid       tan        tan        )
 ( display       tanvLine_S                   vLine      solid       tan        tan        )
 ( display       violet                       blank      solid       violet     violet     )
 ( display       white                        blank      solid       white      white      )
 ( display       whiteX_S                     X          solid       white      white      )
 ( display       whiteX_SB                    X          solid       white      whiteB     )
 ( display       whitecrossdashed             cross      dashed      white      white      )
 ( display       whitedashed_L                blank      dashed      white      white      )
 ( display       whitedot4_S                  dot4       solid       white      white      )
 ( display       whiteslash_S                 slash      solid       white      white      )
 ( display       whitesolid_S                 solid      solid       white      white      )
 ( display       whitetriangle_S              triangle   solid       white      white      )
 ( display       winColor2winColor1crossthickLine   cross      thickLine   winColor2  winColor1  )
 ( display       winColor2winColor1daggerthickLine   dagger     thickLine   winColor2  winColor1  )
 ( display       winColor3XthickLine2         X          thickLine2  winColor3  winColor3  )
 ( display       winColor3dot3_S              dot3       solid       winColor3  winColor3  )
 ( display       winColor3dot3dashed          dot3       dashed      winColor3  winColor3  )
 ( display       winColor3stipple3_S          stipple3   solid       winColor3  winColor3  )
 ( display       winColor4brickdashDot        brick      dashDot     winColor4  winColor4  )
 ( display       winColor5dashed_L            blank      dashed      winColor5  winColor5  )
 ( display       winColor5dashed_LB           blank      dashed      winColor5B winColor5B )
 ( display       winColor5dot3dashed          dot3       dashed      winColor5  winColor5  )
 ( display       yellow                       blank      solid       yellow     yellow     )
 ( display       yellowX_S                    X          solid       yellow     yellow     )
 ( display       yellowX_SB                   X          solid       yellow     yellowB    )
 ( display       yellowcrossthickLine         cross      thickLine   yellow     yellow     )
 ( display       yellowdot4_S                 dot4       solid       yellow     yellow     )
 ( display       yellowdots_S                 dots       solid       yellow     yellow     )
 ( display       yellowhLine_S                hLine      solid       yellow     yellow     )
 ( display       yellowshortDash_L            blank      shortDash   yellow     yellow     )
 ( display       yellowsolid_S                solid      solid       yellow     yellow     )
 ( display       cadetBluesolid_S             solid      solid       cadetBlue  cadetBlue  )

( display M9_odummy	dot4	thickLine2	gold	gold )
( display M10_dummy3	X	thickLine2	cyan	cyan )
( display M10_test7	X	thickLine2	cyan	cyan )
( display M10_testb	X	thickLine2	cyan	cyan )
( display M10_dummyc	X	thickLine2	cyan	cyan )
( display M10_dummya	X	thickLine2	cyan	cyan )
( display M10_test9	X	thickLine2	cyan	cyan )
( display M10_dummyl	X	thickLine2	cyan	cyan )
( display M10_pin	X	thickLine2	cyan	cyan )
( display M10_test4	X	thickLine2	cyan	cyan )
( display VIA9_drawing	stipple3	solid	cyan	cyan )
( display M10_dummye	X	thickLine2	cyan	cyan )
( display M10_drawing	hLine	solid	forest	forest )
( display VIA9_blockage	stipple3	solid	cyan	cyan )
( display M10_testc	X	thickLine2	cyan	cyan )
( display M10_dummy8	X	thickLine2	cyan	cyan )
( display M10_dummy5	X	thickLine2	cyan	cyan )
( display M10_NV	X	thickLine2	cyan	cyan )
( display M10_dummyh	X	thickLine2	cyan	cyan )
( display M10_blockage	hLine	solid	forest	forest )
( display M10_dummy7	X	thickLine2	cyan	cyan )
( display M10_testh	X	thickLine2	cyan	cyan )
( display M10_dummy2	X	thickLine2	cyan	cyan )
( display M10_test1	X	thickLine2	cyan	cyan )
( display M10_dummyk	X	thickLine2	cyan	cyan )
( display M10_teste	X	thickLine2	cyan	cyan )
( display M10_testj	X	thickLine2	cyan	cyan )
( display M10_dummyj	X	thickLine2	cyan	cyan )
( display M10_dummy9	X	thickLine2	cyan	cyan )
( display M10_dummym	X	thickLine2	cyan	cyan )
( display VIA9_net	X	solid	white	white )
( display M10_grid	hLine	solid	forest	forest )
( display M10_dummyb	X	thickLine2	cyan	cyan )
( display M10_dummyd	X	thickLine2	cyan	cyan )
( display VIA9_grid	stipple3	solid	cyan	cyan )
( display VIA9_boundary	blank	solid	white	white )
( display M10_boundary	dot4	solid	white	white )
( display M10_test8	X	thickLine2	cyan	cyan )
( display M10_net	X	solid	white	white )
( display M10_testd	X	thickLine2	cyan	cyan )
( display M10_dummyf	X	thickLine2	cyan	cyan )
( display M10_dummy6	X	thickLine2	cyan	cyan )
( display M10_testa	X	thickLine2	cyan	cyan )
( display M10_test3	X	thickLine2	cyan	cyan )
( display M10_test2	X	thickLine2	cyan	cyan )
( display M10_prob	dot4	thickLine2	cyan	cyan )
( display M10_dummyg	X	thickLine2	cyan	cyan )
( display M10_VNV	X	thickLine2	cyan	cyan )
( display M10_testi	X	thickLine2	cyan	cyan )
( display M10_text	X	thickLine2	cyan	cyan )
( display M10_test5	X	thickLine2	cyan	cyan )
( display M10_test0	X	thickLine2	cyan	cyan )
( display M10_dummyi	X	thickLine2	cyan	cyan )
( display VIA9_pin	solid	solid	yellow	yellow )
( display M10_dummy1	X	thickLine2	cyan	cyan )
( display M10_dummy4	X	thickLine2	cyan	cyan )
( display M10_dummy	X	thickLine2	cyan	cyan )
( display ex_R_rule_analog	blank	dashed	orange	orange )
( display POS_dummy2	brick	dashDot	cyan	cyan )
( display BJTDMY_dummy2	triangle	solid	green	green )
( display RFDMY_test5	X	thickLine	red	red )
( display M6_test9	X	thickLine2	tan	tan )
( display RFDMY_dummye	X	thickLine	red	red )
( display FLASH_dummyj	blank	solid	lime	lime )
( display FLASH_dummyg	blank	solid	lime	lime )
( display M1_dummyf	X	thickLine2	cyan	cyan )
( display HV_nwell	blank	thickLine	magenta	magenta )
( display M3_NV	X	thickLine2	forest	forest )
( display AP_test2	dots	solid	gray	gray )
( display TGO_IO_drawing	blank	solid	red	red )
( display boundary_drawing	blank	dashed	red	red )
( display CO_test6	cross	solid	blue	blue )
( display CDUDMY_test4	cross	solid	green	green )
( display M6_dummya	X	thickLine2	tan	tan )
( display prBoundary_boundary	blank	dashed	purple	purple )
( display SRAMDMY_dummy9	solid	solid	orange	orange )
( display Row_label	blank	solid	cyan	cyan )
( display M3_test2	X	thickLine2	forest	forest )
( display M8_test1	X	thickLine2	cream	cream )
( display UHVT_P_drawing	blank	solid	navy	navy )
( display ICOVL_testb	cross	solid	green	green )
( display OD_drawing2	dot2	solid	cream	cream )
( display ICOVL_dummyi	cross	solid	green	green )
( display SRM1_dummya	blank	solid	forest	forest )
( display SRM1_dummyf	blank	solid	forest	forest )
( display M6_test8	X	thickLine2	tan	tan )
( display M7_testh	X	thickLine2	lime	lime )
( display INDDMY_dummyc	blank	solid	green	green )
( display MOMDMY_test7	blank	solid	orange	orange )
( display M4_dummyi	X	thickLine2	slate	slate )
( display M2_blockage	stipple0	solid	gold	gold )
( display PM_drawingf	blank	solid	white	white )
( display NW65_drawing	blank	solid	white	white )
( display M4_dummye	X	thickLine2	slate	slate )
( display M8_dummy5	X	thickLine2	cream	cream )
( display PO_blockage	blank	thickLine2	cream	cream )
( display FLASH_drawing6	blank	solid	lime	lime )
( display M3_blockage	dot4	solid	forest	forest )
( display RV_blockage	x	solid	forest	forest )
( display INDDMY_dummy7	blank	solid	violet	violet )
( display M9_teste	X	thickLine2	lime	lime )
( display M2_grid	stipple0	solid	gold	gold )
( display HV_dummyo	dot4	thickLine2	cyan	cyan )
( display M7_NV	X	thickLine2	lime	lime )
( display MRAM_drawinga	X	thickLine	red	red )
( display M1_NV	X	thickLine2	cyan	cyan )
( display CTMDMY_drawing1	blank	solid	green	green )
( display CB2_WB_drawing1	blank	solid	white	white )
( display ICOVL_drawing3	cross	solid	green	green )
( display SRM_drawing1	blank	solid	tan	tan )
( display annotate_drawing4	blank	solid	yellow	yellow )
( display FLASH1_dummyd	blank	solid	lime	lime )
( display FLASH2_dummya	dot1	solid	green	green )
( display M7_dummyc	X	thickLine2	lime	lime )
( display PM_drawing2	blank	solid	white	white )
( display M2_dummy7	X	thickLine2	gold	gold )
( display MRDMY_dummyi	dot4	solid	yellow	yellow )
( display CO_test4	cross	solid	blue	blue )
( display INDDMY_rad	blank	solid	joy1	joy1 )
( display MRDMY_drawinge	dot4	solid	yellow	yellow )
( display M5_test0	X	thickLine2	winColor3	winColor3 )
( display RAM1TDMY_drawing4	blank	solid	magenta	magenta )
( display M9_test8	X	thickLine2	lime	lime )
( display VIA6_blockage	x	solid	gold	gold )
( display PMET_CUT_drawing	blank	solid	white	white )
( display AP_dummyb	X	thickLine2	gold	gold )
( display M9_dummy9	X	thickLine2	lime	lime )
( display VAR_drawing	blank	solid	yellow	yellow )
( display PM_drawingd	blank	solid	white	white )
( display SRM_LOP_dummy1	X	solid	green	green )
( display UBM_drawing	dot1	thickLine	gray	gray )
( display M8_dummyk	X	thickLine2	cream	cream )
( display M2_dummya	X	thickLine2	gold	gold )
( display VIA5_blockage	slash	solid	cream	cream )
( display MOMDMY_test1	blank	solid	orange	orange )
( display M6_dummy6	X	thickLine2	tan	tan )
( display HV_testj	blank	thickLine	magenta	magenta )
( display M2_dummyb	X	thickLine2	gold	gold )
( display M2_test0	X	thickLine2	gold	gold )
( display MRAM_dummya	blank	solid	magenta	magenta )
( display ANVT_drawing	blank	solid	red	red )
( display M6_dummyc	X	thickLine2	tan	tan )
( display MRDMY_test3	dot4	solid	yellow	yellow )
( display FLASH1_drawing6	blank	solid	lime	lime )
( display PO_drawing2	dot2	solid	forest	forest )
( display VTSUH_P_drawing	dot4	thickLine2	cyan	cyan )
( display OD_25_ovrdrv	cross	thickLine	winColor2	winColor1 )
( display M8_prob	dot4	thickLine2	cyan	cyan )
( display VIAEXCL_dummy2	blank	solid	violet	violet )
( display M6_dummyl	X	thickLine2	tan	tan )
( display RFDMY_test1	X	thickLine	red	red )
( display CO_testc	cross	solid	cyan	cyan )
( display hilite_drawing6	blank	solid	orange	orange )
( display SRM1_dummy4	blank	solid	forest	forest )
( display FLASH2_dummyf	blank	solid	lime	lime )
( display M8_testc	X	thickLine2	cream	cream )
( display DVIAEXCL_dummy7	blank	solid	orange	orange )
( display M7_blockage	dot4	solid	lime	lime )
( display M2_net	X	solid	white	white )
( display DVIAEXCL_dummy6	blank	solid	orange	orange )
( display NWDMY_lvs	cross	dashed	orange	orange )
( display M9_dummy2	X	thickLine2	lime	lime )
( display M6_test2	X	thickLine2	tan	tan )
( display SRM_drawinge	blank	solid	tan	tan )
( display AP_drawingc	dots	solid	gray	gray )
( display POFUSE_drawing1	dot4	solid	blue	blue )
( display AP_drawinge	dots	solid	gray	gray )
( display M7_dummy2	X	thickLine2	lime	lime )
( display M3_testb	X	thickLine2	forest	forest )
( display OVERLAP_drawing	blank	solid	orange	orange )
( display R_rule_require	dots	solid	red	red )
( display MPOL_drawing	cross	thickLine	blue	blue )
( display MFUSE_drawing	dot4	solid	magenta	magenta )
( display RMDMY_drawing8	brick	dashDot	cream	cream )
( display UBM_dummya	dot1	thickLine	gray	gray )
( display FEDRAM_drawing	blank	solid	red	red )
( display DNW_drawing	blank	solid	tan	tan )
( display CDUDMY_drawing7	cross	solid	green	green )
( display M8_dummy3	X	thickLine2	cream	cream )
( display HIA_DUMMY_drawing	blank	solid	magenta	magenta )
( display OD_33_drawing	dot3	thickLine	winColor2	winColor1 )
( display M1_teste	X	thickLine2	cyan	cyan )
( display M5_testj	X	thickLine2	winColor3	winColor3 )
( display VTL_N_drawing	blank	thickLine2	blue	blue )
( display RV_drawingc	x	solid	forest	forest )
( display unset_drawing	blank	solid	forest	forest )
( display M7_drawing	dot4	solid	lime	lime )
( display NT_N_Ncap_NTN	blank	shortDash	tan	tan )
( display AVT_drawing	blank	solid	red	red )
( display MRDMY_test7	dot4	solid	yellow	yellow )
( display TFRSDMY_drawing	blank	solid	white	white )
( display VIA8_grid	stipple3	solid	gold	gold )
( display LVSDMY_dummy6	blank	dashed	green	green )
( display INDDMY_dummy1	blank	solid	violet	violet )
( display DSDDMY_test5	blank	dashed	winColor5	winColor5 )
( display M9_testb	X	thickLine2	lime	lime )
( display TSV_drawinga	X	thickLine	red	red )
( display SRM_test9	blank	solid	forest	forest )
( display NW_drawing	blank	thickLine2	cream	cream )
( display ICOVL_dummy7	cross	solid	green	green )
( display CI_CAP_drawing	blank	solid	yellow	yellow )
( display M7_dummy4	X	thickLine2	lime	lime )
( display SRAMDMY_periphery_g	blank	solid	magenta	magenta )
( display M1_dummya	X	thickLine2	cyan	cyan )
( display DIODMY_dummya	blank	solid	white	white )
( display POFUSE_drawing	dot4	solid	magenta	magenta )
( display RFDMY_dummy5	X	thickLine	red	red )
( display SENDMY_drawing	blank	solid	navy	navy )
( display SRM_DOD_dummy6	blank	solid	red	red )
( display M1_dummy1	X	thickLine2	cyan	cyan )
( display VIA0DMY_dummy	blank	thickLine2	blue	blue )
( display RFDMY_dummy4	X	thickLine	red	red )
( display Unrouted_drawing4	blank	dashed	orange	orange )
( display MFUSE_drawing2	dot4	solid	blue	blue )
( display HVD_n_a	blank	shortDash	gray	gray )
( display SDI_drawing5	blank	solid	white	white )
( display FLASH1_dummym	blank	solid	lime	lime )
( display ODSIDMY_drawing	blank	solid	yellow	yellow )
( display M6_dummy8	X	thickLine2	tan	tan )
( display M9_dummyg	X	thickLine2	lime	lime )
( display M2_text	X	thickLine2	cyan	cyan )
( display M8_boundary	dot4	solid	white	white )
( display M2_test7	X	thickLine2	gold	gold )
( display CBRAM_drawing3	blank	solid	red	red )
( display OD_18_udrdrv	blank	thickLine2	blue	blue )
( display VIA3_boundary	blank	solid	white	white )
( display VIA3_net	X	solid	white	white )
( display SRM_dummyi	blank	solid	forest	forest )
( display M4_testh	X	thickLine2	slate	slate )
( display eVTL_N_drawing	blank	thickLine2	blue	blue )
( display AP_grid	dots	solid	gray	gray )
( display M1_blockage	stipple3	solid	cyan	cyan )
( display M2_testd	X	thickLine2	gold	gold )
( display VIA1_grid	X	solid	yellow	yellow )
( display M6_testi	X	thickLine2	tan	tan )
( display P3_drawing	dot4	solid	lime	lime )
( display ex_R_rule_guideline	blank	dashed	green	green )
( display M2_dummy9	X	thickLine2	gold	gold )
( display UBM_dummy1	dot1	thickLine	gray	gray )
( display DMEXCL_dummy5	blank	solid	orange	orange )
( display MRAM_test7	blank	solid	lime	lime )
( display PW_drawing1	blank	shortDash	cream	cream )
( display MOMDMY_dummyb	blank	solid	orange	orange )
( display DSDDMY_drawing8	blank	dashed	cream	cream )
( display VAR_drawingb	blank	solid	yellow	yellow )
( display CSRDMY_drawing3	blank	solid	blue	blue )
( display CLDD_drawing	blank	solid	white	white )
( display TCLO_dummy3	cross	solid	cyan	cyan )
( display FLASH1_drawing1	blank	solid	lime	lime )
( display FLASH2_drawing9	blank	shortDash	magenta	magenta )
( display HV_dummy8	dot4	thickLine2	cyan	cyan )
( display SRM_dummyc	blank	solid	forest	forest )
( display FLASH_test8	blank	solid	lime	lime )
( display Cannotoccupy_drawing	X	thickLine	red	red )
( display TOPMCON_drawing	hLine	solid	lime	lime )
( display SRM_DOD_dummyg	blank	solid	red	red )
( display M8_test3	X	thickLine2	cream	cream )
( display SRAMDMY_periphery	blank	solid	magenta	magenta )
( display M7_dummy	X	thickLine2	lime	lime )
( display M4_dummyb	X	thickLine2	slate	slate )
( display MRDMY_dummya	dot4	solid	yellow	yellow )
( display annotate_drawing7	blank	solid	red	red )
( display M9_dummym	X	thickLine2	lime	lime )
( display SRM_test0	blank	solid	forest	forest )
( display PO_test6	cross	thickLine	silver	silver )
( display M2_dummyh	X	thickLine2	gold	gold )
( display M2_dummyk	X	thickLine2	gold	gold )
( display VAR_drawingc	blank	solid	yellow	yellow )
( display M4_prob	dot4	thickLine2	cyan	cyan )
( display SRM_DPO_dummy2	blank	solid	blue	blue )
( display M5_dummyk	X	thickLine2	winColor3	winColor3 )
( display CDUDMY_dummy7	cross	solid	green	green )
( display Canplace_drawing	blank	solid	cyan	cyan )
( display BJTDMY_dummy	triangle	solid	green	green )
( display CTM_drawing1	triangle	solid	purple	purple )
( display MRAM_drawing6	blank	solid	navy	navy )
( display FLASH1_dummya	blank	solid	lime	lime )
( display ICOVL_dummy2	cross	solid	green	green )
( display TCLO_dummy6	cross	solid	cyan	cyan )
( display TFRDUMMY_8_drawing	blank	dashed	purple	purple )
( display FLASH2_test2	dots	solid	gray	gray )
( display P10V_drawing	blank	shortDash	magenta	magenta )
( display HVD_drawing	blank	thickLine	magenta	magenta )
( display ESD1DMY_drawing	blank	solid	purple	purple )
( display PM_rule1	blank	solid	white	white )
( display VIA4_pin	solid	solid	yellow	yellow )
( display M8_dummym	X	thickLine2	cream	cream )
( display VIA2_blockage	hLine	solid	yellow	yellow )
( display VIA7_net	X	solid	white	white )
( display TFRDUMMY_drawing	X	thickLine	red	red )
( display IMSOR4_dummy7	blank	solid	red	red )
( display SRM_dummy3	blank	solid	forest	forest )
( display RFDMY_test3	X	thickLine	red	red )
( display RH_drawing4	brick	dashDot	tan	tan )
( display M3_dummyb	X	thickLine2	forest	forest )
( display VIA5_net	X	solid	white	white )
( display PO_13_drawing	cross	thickLine	blue	blue )
( display POS_plus3	brick	dashDot	forest	forest )
( display INDDMY_dummy5	blank	solid	purple	purple )
( display INDDMY_drawingd	blank	solid	cream	cream )
( display RAM1TDMY_drawing2	blank	solid	magenta	magenta )
( display M6_dummy1	X	thickLine2	tan	tan )
( display FLASH1_dummyc	blank	solid	lime	lime )
( display FLASH_test0	blank	solid	lime	lime )
( display DMY_SRK_drawing	blank	solid	red	red )
( display OD_test5	backSlash	solid	purple	purple )
( display VIA7_dummy	vLine	solid	tan	tan )
( display OXR_hv	blank	solid	red	red )
( display AP_drawing3	dots	solid	gray	gray )
( display CDUDMY_test6	cross	solid	green	green )
( display RMDMY_drawingc	brick	dashDot	orange	orange )
( display M7_test3	X	thickLine2	lime	lime )
( display MRDMY_dummyk	dot4	solid	yellow	yellow )
( display instance_drawing	blank	solid	red	red )
( display M1_testh	X	thickLine2	cyan	cyan )
( display AP_dummyd	X	thickLine2	gold	gold )
( display M5_VNV	X	thickLine2	winColor3	winColor3 )
( display SRM1_dummyc	blank	solid	forest	forest )
( display M8_testa	X	thickLine2	cream	cream )
( display SRM1_dummyd	blank	solid	forest	forest )
( display RMDMY_drawing4	brick	dashDot	slate	slate )
( display SRM_test4	blank	solid	forest	forest )
( display M8_dummyg	X	thickLine2	cream	cream )
( display CDUDMY_drawing	cross	solid	green	green )
( display FLASH2_test9	blank	solid	silver	silver )
( display VTL_N_18_drawing	dot4	thickLine2	cyan	cyan )
( display OD1T_drawing	dot1	solid	magenta	magenta )
( display MOMDMY_dummy3	blank	solid	joy1	joy1 )
( display M0_drawing	blank	thickLine2	green	green )
( display ESD2DMY_drawing	blank	solid	cyan	cyan )
( display FINFET_dummy	stipple0	solid	cream	cream )
( display annotate_drawing2	blank	solid	lime	lime )
( display BTSV_drawing4	X	thickLine	red	red )
( display RPO_drawing	blank	solid	forest	forest )
( display MOMDMY_dummy5	blank	solid	tan	tan )
( display M9_dummy4	X	thickLine2	lime	lime )
( display CO_boundary	solid	solid	lime	lime )
( display M1_dummy3	X	thickLine2	cyan	cyan )
( display drive_drawing	blank	solid	blue	blue )
( display M3_testd	X	thickLine2	forest	forest )
( display M6_dummye	X	thickLine2	tan	tan )
( display SRM_DPO_dummy4	blank	solid	blue	blue )
( display M5_dummyh	X	thickLine2	winColor3	winColor3 )
( display VT_N_drawing2	dot4	thickLine2	cyan	cyan )
( display SRM_LOP_dummy7	blank	solid	tan	tan )
( display M4_dummy6	X	thickLine2	slate	slate )
( display POS_dummy1	brick	dashDot	cyan	cyan )
( display FLASH_drawing2	blank	solid	lime	lime )
( display M4_dummyl	X	thickLine2	slate	slate )
( display M5_drawing	dot3	solid	winColor3	winColor3 )
( display ICOVL_dummy9	cross	solid	green	green )
( display WAIVER_drawing4	blank	solid	red	red )
( display M2_test2	X	thickLine2	gold	gold )
( display VIAEXCL_dummya	blank	solid	violet	violet )
( display instance_label	blank	solid	gold	gold )
( display M4_net	X	solid	white	white )
( display M3_net	X	solid	white	white )
( display SENDMY_drawing1	blank	solid	navy	navy )
( display M9_blockage	hLine	solid	lime	lime )
( display DSDDMY_drawing1	blank	dashed	cyan	cyan )
( display FLASH2_drawing4	blank	solid	cream	cream )
( display FLASH1_dummyf	blank	solid	lime	lime )
( display CO_testa	cross	solid	cyan	cyan )
( display SRM1_dummyk	blank	solid	forest	forest )
( display MRAM_drawingc	X	solid	green	green )
( display grid_drawing	blank	solid	white	white )
( display VTUL_N_drawing	blank	thickLine2	blue	blue )
( display align_drawing	blank	solid	tan	tan )
( display DGATE_drawing	blank	solid	red	red )
( display VIA3_grid	vLine	solid	purple	purple )
( display M2_VNV	X	thickLine2	gold	gold )
( display CB2_drawing2	blank	solid	white	white )
( display PO_text	X	thickLine	blue	blue )
( display SRAMDMY_dummy6	solid	solid	orange	orange )
( display SRAMDMY_drawing2	solid	solid	orange	orange )
( display RFDMY_drawingd	blank	dashed	purple	purple )
( display TCDDMY_drawing8	cross	solid	green	green )
( display hilite_drawing4	blank	solid	orange	orange )
( display axis_drawing	blank	solid	white	white )
( display FLASH2_dummy	blank	solid	white	white )
( display SOI_NLDDV	dots	solid	gray	gray )
( display M3_dummy2	X	thickLine2	forest	forest )
( display MRDMY_test5	dot4	solid	yellow	yellow )
( display SRAMDMY_dummy7	solid	solid	orange	orange )
( display AP_drawinga	dots	solid	gray	gray )
( display M7_dummy9	X	thickLine2	lime	lime )
( display RMDMY_drawing2	brick	dashDot	gold	gold )
( display SNCT_drawing	stipple0	solid	cream	cream )
( display WBDMY_drawing	dot1	solid	yellow	yellow )
( display MRDMY_test6	dot4	solid	yellow	yellow )
( display FLASH1_drawing7	blank	solid	lime	lime )
( display VTNCEL_drawing	blank	solid	gray	gray )
( display ex_R_rule_recommend	blank	dashed	pink	pink )
( display DMEXCL_dummy8	blank	solid	orange	orange )
( display M7_dummyl	X	thickLine2	lime	lime )
( display VIAEXCL_dummyc	blank	solid	violet	violet )
( display M9_net	X	solid	white	white )
( display SRM_testb	blank	solid	gold	gold )
( display SRM_test6	blank	solid	forest	forest )
( display RV_drawing1	x	solid	forest	forest )
( display HV_drawing	blank	thickLine	magenta	magenta )
( display HBC_BOT_drawing	blank	solid	red	red )
( display M7_prob	dot4	thickLine2	cyan	cyan )
( display BVZ_drawing	blank	solid	red	red )
( display RPO_drawing2	blank	solid	forest	forest )
( display annotate_drawing	blank	solid	orange	orange )
( display M5_testh	X	thickLine2	winColor3	winColor3 )
( display M8_testj	X	thickLine2	cream	cream )
( display M3_test9	X	thickLine2	forest	forest )
( display SRM_drawing8	blank	solid	blue	blue )
( display MRDMY_drawing6	dot4	solid	yellow	yellow )
( display RV_drawinga	x	solid	forest	forest )
( display M4_test2	X	thickLine2	slate	slate )
( display designFlow_drawing7	solid	solid	cyan	cyan )
( display FLASH1_drawingb	blank	solid	lime	lime )
( display SOI_MKR_soibt	dot1	solid	green	green )
( display MRAM_test5	blank	solid	navy	navy )
( display Unrouted_drawing9	blank	dashed	silver	silver )
( display y4_drawing	blank	dashed	orangeB	orangeB )
( display CBRAM_drawingb	blank	solid	red	red )
( display NP_drawing	blank	solid	gray	gray )
( display M6_drawing	stipple3	solid	tan	tan )
( display M4_dummy	X	thickLine2	slate	slate )
( display M2_dummyd	X	thickLine2	gold	gold )
( display VIAEXCL_dummy4	blank	solid	violet	violet )
( display WAIVER_drawing	blank	solid	red	red )
( display DMEXCL_dummyc	blank	solid	orange	orange )
( display LOWMEDN_drawing	blank	solid	lime	lime )
( display M1_BSD	dot4	solid	yellow	yellow )
( display SRM_drawing3	blank	solid	tan	tan )
( display Unrouted_drawing2	blank	dashed	red	red )
( display FLASH_drawingc	blank	solid	lime	lime )
( display CDUDMY_dummy6	cross	solid	green	green )
( display PO_rule1	cross	thickLine	orange	orange )
( display SCPADDMY_drawing	blank	solid	red	red )
( display M5_dummy2	X	thickLine2	winColor3	winColor3 )
( display M2_dummym	X	thickLine2	gold	gold )
( display IMSOR_dummy6	blank	solid	red	red )
( display FLASH_test6	blank	solid	lime	lime )
( display grid_drawing1	blank	solid	white	white )
( display FLASH2_drawingh	triangle	solid	orange	orange )
( display CBRAM_drawing5	blank	solid	red	red )
( display BMZ2_pin	blank	solid	red	red )
( display SEALRING_drawing1	blank	solid	gold	gold )
( display M4_dummy9	X	thickLine2	slate	slate )
( display VIA4_boundary	blank	solid	white	white )
( display CB_drawing	dots	solid	orange	orange )
( display M3_testi	X	thickLine2	forest	forest )
( display MRDMY_dummyp	dot4	solid	yellow	yellow )
( display M1_dummyd	X	thickLine2	cyan	cyan )
( display VTH_PS_drawing	blank	solid	white	white )
( display ICOVL_dummy4	cross	solid	green	green )
( display M9_drawing	hLine	solid	lime	lime )
( display FLASH2_dummym	blank	solid	lime	lime )
( display M2_test5	X	thickLine2	gold	gold )
( display M5_dummye	X	thickLine2	winColor3	winColor3 )
( display FLASH2_drawingd	dot1	thickLine	gray	gray )
( display MRAM_drawingf	blank	solid	lime	lime )
( display ICOVL_drawing5	cross	solid	green	green )
( display HVDMY_drawing	blank	solid	red	red )
( display M9_dummyf	X	thickLine2	lime	lime )
( display VSSDMY_drawing	blank	solid	navy	navy )
( display CB2_drawing	dots	solid	yellow	yellow )
( display TCLO_test3	cross	solid	blue	blue )
( display SRM_drawingb	blank	solid	tan	tan )
( display VIA2_net	X	solid	white	white )
( display MOMDMY_drawingb	blank	dashed	forest	forest )
( display M6_odummy	dot4	thickLine2	tan	tan )
( display SRM_DOD_dummye	blank	solid	red	red )
( display M4_test4	X	thickLine2	slate	slate )
( display INDDMY_drawingc	blank	solid	yellow	yellow )
( display M3_dummyi	X	thickLine2	forest	forest )
( display DSDDMY_test7	blank	dashed	winColor5	winColor5 )
( display M8_dummyc	X	thickLine2	cream	cream )
( display TCLO_dummy1	cross	solid	cyan	cyan )
( display SRM_dummy5	blank	solid	forest	forest )
( display ICOVL_drawingf	cross	solid	green	green )
( display TAP_H620_drawing	blank	solid	lime	lime )
( display CBRAM_drawinge	blank	solid	red	red )
( display BJTDMY_hv	triangle	solid	green	green )
( display DVIAEXCL_dummya	blank	solid	orange	orange )
( display INDDMY_drawing5	blank	solid	lime	lime )
( display M5_test9	X	thickLine2	winColor3	winColor3 )
( display BTSV_drawing2	X	thickLine	red	red )
( display MRDMY_dummyf	dot4	solid	yellow	yellow )
( display DIODMY_dummyd	blank	solid	white	white )
( display TCLO_test1	cross	solid	blue	blue )
( display M9_test5	X	thickLine2	lime	lime )
( display HVCORE_pldds	blank	solid	red	red )
( display M4_dummyg	X	thickLine2	slate	slate )
( display annotate_drawing9	blank	solid	green	green )
( display M2_teste	X	thickLine2	gold	gold )
( display M1_test5	X	thickLine2	cyan	cyan )
( display PO_boundary	solid	solid	lime	lime )
( display FINFET_dummya	hLine	solid	lime	lime )
( display AP_net	X	solid	lime	lime )
( display MRDMY_dummyc	dot4	solid	yellow	yellow )
( display M4_NV	X	thickLine2	slate	slate )
( display M3_teste	X	thickLine2	forest	forest )
( display hilite_drawing3	blank	solid	cyan	cyan )
( display Group_drawing	dots	solid	green	green )
( display DSDDMY_drawing3	blank	dashed	forest	forest )
( display SRM_testa	blank	solid	gold	gold )
( display PP_drawing	blank	solid	magenta	magenta )
( display VTD_N_drawing	blank	thickLine2	tan	tan )
( display MRAM_drawing	blank	dashed	purple	purple )
( display CBRAM_drawing1	blank	solid	red	red )
( display VIAEXCL_dummy1	blank	solid	violet	violet )
( display TCLO_dummy8	cross	solid	cyan	cyan )
( display FLASH2_testd	blank	solid	magenta	magenta )
( display M5_dummy3	X	thickLine2	winColor3	winColor3 )
( display VIA8_boundary	blank	solid	white	white )
( display M1_dummyj	X	thickLine2	cyan	cyan )
( display M5_testb	X	thickLine2	winColor3	winColor3 )
( display OD_test3	backSlash	solid	purple	purple )
( display TCLO_dummy9	cross	solid	cyan	cyan )
( display RH_drawing2	X	thickLine2	cyan	cyan )
( display VIA5_grid	slash	solid	cream	cream )
( display M9_test1	X	thickLine2	lime	lime )
( display PM_drawinga	blank	solid	white	white )
( display MRDMY_drawinga	dot4	solid	yellow	yellow )
( display HV_pwell	blank	thickLine	magenta	magenta )
( display CROWN_drawing2	stipple0	solid	lime	lime )
( display OD_18_ovrdrv	blank	thickLine2	green	green )
( display CBB_drawing	blank	solid	red	red )
( display P1VNIX_drawing	blank	solid	orange	orange )
( display hilite_drawing1	blank	solid	magenta	magenta )
( display FLASH_testb	blank	solid	lime	lime )
( display SRM_test7	blank	solid	forest	forest )
( display M3_testc	X	thickLine2	forest	forest )
( display M8_test5	X	thickLine2	cream	cream )
( display INDDMY_test1	blank	solid	lime	lime )
( display Unrouted_drawing8	blank	dashed	gold	gold )
( display INDDMY_drawing6	blank	solid	yellow	yellow )
( display MOMDMY_test4	blank	solid	orange	orange )
( display CROWN_drawing1	stipple0	solid	lime	lime )
( display MRDMY_dummy6	dot4	solid	yellow	yellow )
( display HVD_p_b	blank	shortDash	magenta	magenta )
( display M9_dummy5	X	thickLine2	lime	lime )
( display M9_testa	X	thickLine2	lime	lime )
( display OD_test2	backSlash	solid	purple	purple )
( display FINFET_boundary	blank	solid	orange	orange )
( display FINFET_dummyg	hLine	solid	lime	lime )
( display M2_dummyl	X	thickLine2	gold	gold )
( display PV_N_drawing	blank	solid	tan	tan )
( display M0BLK_dummy	blank	solid	red	red )
( display M1_dummy6	X	thickLine2	cyan	cyan )
( display M4_dummy4	X	thickLine2	slate	slate )
( display MRDMY_test0	dot4	solid	yellow	yellow )
( display M8_drawing	cross	solid	cream	cream )
( display M4_dummya	X	thickLine2	slate	slate )
( display FLASH2_test0	dot2	solid	cream	cream )
( display BJTC_drawing	dots	solid	pink	pink )
( display SRM_LOP_dummy6	blank	solid	tan	tan )
( display M5_dummyg	X	thickLine2	winColor3	winColor3 )
( display TCDDMY_drawing1	cross	solid	green	green )
( display SENDMY_drawinga	blank	solid	navy	navy )
( display TSV_pin	blank	solid	red	red )
( display VTL_P25_S_drawing	blank	solid	orange	orange )
( display RV_BSL	brick	dashDot	blue	blue )
( display M4_dummyj	X	thickLine2	slate	slate )
( display M6_dummyh	X	thickLine2	tan	tan )
( display M7_testj	X	thickLine2	lime	lime )
( display SRM_dummyg	blank	solid	forest	forest )
( display TCLO_test7	cross	solid	blue	blue )
( display INDDMY_dummyf	blank	solid	green	green )
( display EDRAM_drawing	solid	solid	yellow	yellow )
( display M5_dummy9	X	thickLine2	winColor3	winColor3 )
( display POS_dummya	brick	dashDot	cyan	cyan )
( display DMEXCL_dummya	blank	solid	orange	orange )
( display ICOVL_test0	cross	solid	green	green )
( display M5_dummyb	X	thickLine2	winColor3	winColor3 )
( display VTUL_NS_drawing	blank	solid	white	white )
( display M7_dummyi	X	thickLine2	lime	lime )
( display CBRAM_drawingh	blank	solid	red	red )
( display M7_testd	X	thickLine2	lime	lime )
( display CBRAM_drawing8	blank	solid	red	red )
( display FLASH1_dummyi	blank	solid	lime	lime )
( display ZENERDMY_hv	blank	solid	red	red )
( display MOMDMY_dummy6	blank	solid	orange	orange )
( display MRDMY_drawing4	dot4	solid	yellow	yellow )
( display M2_testi	X	thickLine2	gold	gold )
( display AP_test1	dots	solid	gray	gray )
( display CTMDMY_drawing5	blank	solid	green	green )
( display SRM_drawing2	blank	solid	tan	tan )
( display TCDDMY_drawing4	cross	solid	green	green )
( display TSV_dummy	blank	solid	red	red )
( display MRDMY_drawing3	dot4	solid	yellow	yellow )
( display DSDDMY_test0	blank	dashed	winColor5	winColor5 )
( display VIA6_grid	x	solid	gold	gold )
( display RRAM_drawing6	blank	solid	red	red )
( display M5_grid	dot3	solid	winColor3	winColor3 )
( display designFlow_drawing4	solid	solid	black	black )
( display M3_test5	X	thickLine2	forest	forest )
( display EHVT_P_drawing	blank	thickLine	magenta	magenta )
( display CO_drawing	cross	solid	green	green )
( display INDDMY_drawing9	blank	solid	yellow	yellow )
( display M2_testh	X	thickLine2	gold	gold )
( display M4_testb	X	thickLine2	slate	slate )
( display VIAEXCL_dummy	blank	solid	violet	violet )
( display DVIAEXCL_dummy2	blank	solid	orange	orange )
( display M9_dummy7	X	thickLine2	lime	lime )
( display MOMDMY_dummy2	blank	dashed	winColor3	winColor3 )
( display PO_dummy	X	thickLine	blue	blue )
( display CSRDMY_drawing	blank	solid	blue	blue )
( display M8_dummy6	X	thickLine2	cream	cream )
( display M4_blockage	dot4	solid	slate	slate )
( display M1_BSP	blank	solid	green	green )
( display SRM_dummy8	blank	solid	forest	forest )
( display CTM_drawing	triangle	solid	purple	purple )
( display edgeLayer_pin	blank	solid	yellow	yellow )
( display M1_test9	X	thickLine2	cyan	cyan )
( display WAIVER_drawing1	blank	solid	red	red )
( display RMDMY_drawing6	brick	dashDot	tan	tan )
( display FLASH_drawing4	blank	solid	lime	lime )
( display SRM_DOD_dummy3	blank	solid	red	red )
( display SRM1_dummy9	blank	solid	forest	forest )
( display RV_boundary	solid	solid	lime	lime )
( display CB_drawinga	dots	solid	orange	orange )
( display M2_dummy2	X	thickLine2	gold	gold )
( display M3_dummym	X	thickLine2	forest	forest )
( display RFDMY_dummy7	X	thickLine	red	red )
( display ICOVL_dummy6	cross	solid	green	green )
( display AP_drawing2	dots	solid	gray	gray )
( display SRM_LOP_dummy3	blank	solid	red	red )
( display PP_NSC3_drawing	blank	solid	navy	navy )
( display M9_dummyi	X	thickLine2	lime	lime )
( display VTLN_P25_drawing	blank	solid	red	red )
( display M6_grid	stipple3	solid	tan	tan )
( display VIAEXCL_dummy6	blank	solid	violet	violet )
( display FLASH2_dummyc	dot2	solid	red	red )
( display CDUDMY_drawing3	cross	solid	green	green )
( display M9_grid	hLine	solid	lime	lime )
( display FLASH_test3	blank	solid	lime	lime )
( display ESD3_dummy1	blank	solid	navy	navy )
( display PV_P_drawing	blank	solid	orange	orange )
( display M8_dummyi	X	thickLine2	cream	cream )
( display CDUDMY_test8	cross	solid	green	green )
( display M3_dummy7	X	thickLine2	forest	forest )
( display CBRAM_rule1	blank	solid	red	red )
( display MRDMY_dummyn	dot4	solid	yellow	yellow )
( display border_drawing	blank	solid	tan	tan )
( display M3_test7	X	thickLine2	forest	forest )
( display INDDMY_dummyb	blank	solid	green	green )
( display M8_test7	X	thickLine2	cream	cream )
( display M6_VNV	X	thickLine2	tan	tan )
( display FLASH2_dummyh	blank	solid	lime	lime )
( display CBD_drawinga	blank	solid	lime	lime )
( display HVII_drawing	blank	solid	blue	blue )
( display M2_test4	X	thickLine2	gold	gold )
( display RODMY_drawing	blank	solid	gold	gold )
( display CSRDMY_drawing4	blank	solid	blue	blue )
( display M2_testa	X	thickLine2	gold	gold )
( display M6_dummy3	X	thickLine2	tan	tan )
( display WAIVER_drawing2	blank	solid	red	red )
( display M8_grid	cross	solid	cream	cream )
( display M7_dummyd	X	thickLine2	lime	lime )
( display MATCHING_MOS_drawing	blank	solid	red	red )
( display M3_test0	X	thickLine2	forest	forest )
( display FLASH2_drawingf	triangle	solid	purple	purple )
( display MRAM_test3	X	solid	green	green )
( display POS_plus1	brick	dashDot	cyan	cyan )
( display FLASH2_test6	blank	solid	purple	purple )
( display INDDMY_dummy3	blank	solid	lime	lime )
( display AP_dummy2	X	thickLine2	gold	gold )
( display SCL_pin	blank	solid	red	red )
( display MOMDMY_testb	blank	solid	orange	orange )
( display FLASH_drawinga	blank	solid	lime	lime )
( display M7_test2	X	thickLine2	lime	lime )
( display softFence_drawing	blank	solid	yellow	yellow )
( display M8_testb	X	thickLine2	cream	cream )
( display MRDMY_rule1	dot4	solid	yellow	yellow )
( display HOTWL_drawing	X	solid	orange	orange )
( display AP_pin	dots	solid	gray	gray )
( display MOMDMY_drawing7	blank	solid	pink	pink )
( display SEALRING_drawing2	blank	solid	gold	gold )
( display M1_drawing	dot4	solid	cyan	cyan )
( display VIA3_drawinga	dot4	solid	cyan	cyan )
( display M7_test9	X	thickLine2	lime	lime )
( display VIA8_drawing	stipple3	solid	gold	gold )
( display FLASH_dummyd	blank	solid	lime	lime )
( display M9_dummyd	X	thickLine2	lime	lime )
( display PO_test2	cross	thickLine	silver	silver )
( display MOMDMY_test6	blank	solid	orange	orange )
( display HBL_TOP_drawing	blank	solid	red	red )
( display RFDMY_drawing9	X	thickLine	red	red )
( display FLASH_test5	blank	solid	lime	lime )
( display M9_testh	X	thickLine2	lime	lime )
( display SRM_drawing5	blank	solid	blue	blue )
( display SRM_DPO_dummy5	blank	solid	blue	blue )
( display AP_BSP	X	thickLine	red	red )
( display ICOVL_testd	cross	solid	green	green )
( display Layer1_drawing1	blank	solid	navy	navy )
( display SRM1_dummy8	blank	solid	forest	forest )
( display M4_testd	X	thickLine2	slate	slate )
( display M3_dummyk	X	thickLine2	forest	forest )
( display LVSDMY_dummy1	blank	dashed	green	green )
( display FLASH_dummye	blank	solid	lime	lime )
( display TFRDUMMY_9_drawing	blank	dashed	purple	purple )
( display M9_NV	X	thickLine2	lime	lime )
( display RFDMY_dummy1	X	thickLine	red	red )
( display OD_18_drawing	dagger	thickLine	winColor2	winColor1 )
( display M6_teste	X	thickLine2	tan	tan )
( display FLASH2_dummyo	blank	solid	lime	lime )
( display M1_net	X	solid	white	white )
( display FINFET_dummyc	hLine	solid	lime	lime )
( display PO_dummy1	X	solid	green	green )
( display MRAM_drawing3	dot4	solid	yellow	yellow )
( display H620_drawing	blank	solid	white	white )
( display LDDBLK_drawinga	blank	solid	orange	orange )
( display M1_dummyh	X	thickLine2	cyan	cyan )
( display RFDMY_drawinge	blank	dashed	purple	purple )
( display M1_dummy4	X	thickLine2	cyan	cyan )
( display HV_drawing4	blank	thickLine	magenta	magenta )
( display FLASH1_dummyq	blank	solid	lime	lime )
( display CBM_drawing1	triangle	solid	orange	orange )
( display INDDMY_drawinga	blank	solid	cream	cream )
( display ICOVL_drawing7	cross	solid	green	green )
( display AP_text	blank	dashed	purple	purple )
( display RPDMY_dummy2	brick	dashDot	pink	pink )
( display M9_test3	X	thickLine2	lime	lime )
( display VTSUH_N_drawing	blank	shortDash	gray	gray )
( display MOMDMY_dummy8	blank	solid	orange	orange )
( display SRM_dummy6	blank	solid	forest	forest )
( display MRDMY_plus1	dot4	solid	yellow	yellow )
( display M4_dummyh	X	thickLine2	slate	slate )
( display SEALRING_drawing	blank	solid	gold	gold )
( display ICOVL_drawinge	cross	solid	green	green )
( display M8_dummy8	X	thickLine2	cream	cream )
( display HVD_test0	blank	shortDash	magenta	magenta )
( display DSDDMY_test2	blank	dashed	winColor5	winColor5 )
( display OD_15_ovrdrv	dot3	solid	purple	purple )
( display ex_R_rule_require	blank	dashed	red	red )
( display PO_testj	cross	thickLine	tan	tan )
( display HVESDCORE_well	blank	solid	red	red )
( display RPDMY_dummy3	blank	solid	yellow	yellow )
( display SRM_drawing7	blank	solid	blue	blue )
( display M2_drawing	stipple0	solid	gold	gold )
( display MRDMY_dummyh	dot4	solid	yellow	yellow )
( display M1_pin	X	thickLine2	cyan	cyan )
( display SRM1_dummy6	blank	solid	forest	forest )
( display TCDDMY_drawing6	cross	solid	green	green )
( display SRM_dummyd	blank	solid	forest	forest )
( display CROWN_drawing	stipple0	solid	lime	lime )
( display NWDMY_drawing	cross	dashed	white	white )
( display DNW_hv	blank	solid	tan	tan )
( display MRAM_drawing1	brick	dashDot	blue	blue )
( display M2_pin	X	thickLine2	cyan	cyan )
( display SEALRING_drawing7	blank	solid	gold	gold )
( display M1_BSL	brick	dashDot	blue	blue )
( display M8_text	X	thickLine2	cyan	cyan )
( display CO_pin	X	solid	lime	lime )
( display FW_al	dot4	solid	lime	lime )
( display VIA1_drawing	X	solid	yellow	yellow )
( display OD_drawing	backSlash	solid	red	red )
( display wire_label	solid	solid	gray	gray )
( display CU_PPI_pin	X	thickLine	blue	blue )
( display M1_test3	X	thickLine2	cyan	cyan )
( display M5_dummy7	X	thickLine2	winColor3	winColor3 )
( display M2_test1	X	thickLine2	gold	gold )
( display MW_drawing	blank	thickLine2	cream	cream )
( display VIA4_net	X	solid	white	white )
( display MRDMY_drawingc	dot4	solid	yellow	yellow )
( display AP_test4	dots	solid	gray	gray )
( display HVPW_drawing	blank	thickLine2	orange	orange )
( display AP_dummy3	X	thickLine2	gold	gold )
( display FLASH1_dummyk	blank	solid	lime	lime )
( display ICOVL_dummyk	cross	solid	green	green )
( display hilite_drawing8	blank	solid	magenta	magenta )
( display snap_drawing	blank	solid	yellow	yellow )
( display POS_drawing1	brick	dashDot	cyan	cyan )
( display M3_dummy	X	thickLine2	forest	forest )
( display M7_dummya	X	thickLine2	lime	lime )
( display M4_test5	X	thickLine2	slate	slate )
( display SOI_NLDDH	blank	solid	white	white )
( display ESDIMP_drawing	blank	solid	navy	navy )
( display M6_test3	X	thickLine2	tan	tan )
( display CO_grid	cross	solid	green	green )
( display M7_grid	dot4	solid	lime	lime )
( display MRDMY_drawing1	dot4	solid	yellow	yellow )
( display SOI_drawing	triangle	solid	purple	purple )
( display OD_12_drawing	blank	shortDash	gray	gray )
( display HBC_TOP_drawing1	blank	solid	red	red )
( display M2_dummyf	X	thickLine2	gold	gold )
( display RFDMY_drawing3	X	thickLine	red	red )
( display SRM_dummym	blank	solid	forest	forest )
( display M5_dummy	X	thickLine2	winColor3	winColor3 )
( display ICOVL_drawing2	cross	solid	green	green )
( display VAR_drawinga	blank	solid	yellow	yellow )
( display device_drawing1	solid	solid	green	green )
( display FLASH1_dummyn	blank	solid	lime	lime )
( display WAIVER_drawing6	blank	solid	red	red )
( display M8_test9	X	thickLine2	cream	cream )
( display M1_dummy8	X	thickLine2	cyan	cyan )
( display BJTDMY_drawing	triangle	solid	green	green )
( display OD_dummy	X	thickLine2	red	red )
( display VIA2_grid	hLine	solid	yellow	yellow )
( display MRDMY_dummy4	dot4	solid	yellow	yellow )
( display Unrouted_drawing6	blank	dashed	blue	blue )
( display DVIAEXCL_dummy9	blank	solid	orange	orange )
( display y0_drawing	blank	dashed	winColor5B	winColor5B )
( display DIODMY_hv	blank	solid	white	white )
( display CTMDMY_drawing3	blank	solid	green	green )
( display designFlow_drawing9	solid	solid	orange	orange )
( display M6_dummyj	X	thickLine2	tan	tan )
( display SRM_dummy1	blank	solid	forest	forest )
( display DMEXCL_dummy4	blank	solid	orange	orange )
( display FLASH1_drawing4	blank	solid	lime	lime )
( display M6_testj	X	thickLine2	tan	tan )
( display SOI_MKR_vpfc	dot2	solid	red	red )
( display M4_dummyc	X	thickLine2	slate	slate )
( display M2_dummy8	X	thickLine2	gold	gold )
( display ARRAY_VT_drawing	blank	solid	white	white )
( display annotate_drawing6	blank	solid	silver	silver )
( display INDDMY_test3	blank	solid	cream	cream )
( display M4_test0	X	thickLine2	slate	slate )
( display OD_test9	backSlash	solid	purple	purple )
( display MOMDMY_drawing2	blank	solid	tan	tan )
( display VIA1_pin	solid	solid	yellow	yellow )
( display M8_pin	X	thickLine2	cyan	cyan )
( display M6_prob	dot4	thickLine2	cyan	cyan )
( display AP_dummy	X	thickLine2	gold	gold )
( display supply_drawing	blank	solid	lime	lime )
( display RV_dummy1	x	solid	forest	forest )
( display RRAM_drawing1	blank	solid	red	red )
( display OD_testb	backSlash	solid	silver	silver )
( display M1_dummyl	X	thickLine2	cyan	cyan )
( display M1_prob	dot4	thickLine2	cyan	cyan )
( display FLASH2_testb	blank	solid	magenta	magenta )
( display N1VNIX_drawing	blank	solid	orange	orange )
( display M2_testc	X	thickLine2	gold	gold )
( display BMZ2_drawing	blank	solid	red	red )
( display FET_n_b	dots	solid	gray	gray )
( display FLASH2_drawinga	blank	solid	gray	gray )
( display M6_testc	X	thickLine2	tan	tan )
( display TCLO_test5	cross	solid	blue	blue )
( display VAR_drawingd	blank	solid	yellow	yellow )
( display MRDMY_dummyl	dot4	solid	yellow	yellow )
( display TFRDUMMY_5_drawing	X	thickLine	red	red )
( display M3_prob	dot4	thickLine2	cyan	cyan )
( display OD_dummy1	X	thickLine2	red	red )
( display OD_dummya	X	thickLine2	red	red )
( display FLASH_dummyb	blank	solid	lime	lime )
( display M9_dummyl	X	thickLine2	lime	lime )
( display M4_testi	X	thickLine2	slate	slate )
( display MRDMY_dummys	blank	dashed	purple	purple )
( display ICOVL_dummya	cross	solid	green	green )
( display FET_src	solid	solid	gray	gray )
( display MOMDMY_drawing9	blank	dashed	gold	gold )
( display MRAM_test1	blank	dashed	purple	purple )
( display POBLK_drawinga	blank	solid	orange	orange )
( display CoWAM_drawing	blank	solid	white	white )
( display CAPDMY_drawing	blank	solid	red	red )
( display ICOVL_dummym	cross	solid	green	green )
( display VTL_N_drawing2	blank	thickLine2	blue	blue )
( display M6_dummy	X	thickLine2	tan	tan )
( display MRDMY_dummy1	dot4	solid	yellow	yellow )
( display INDDMY_dummyd	blank	solid	green	green )
( display SRM1_dummyh	blank	solid	forest	forest )
( display FET_p_a	blank	solid	magenta	magenta )
( display RFDMY_drawingb	blank	dashed	purple	purple )
( display M2_prob	dot4	thickLine2	cyan	cyan )
( display HBL_TOP_dummy2	blank	solid	red	red )
( display M3_test3	X	thickLine2	forest	forest )
( display MRAM_dummyc	blank	dashed	blue	blue )
( display MOMDMY_drawing5	blank	solid	lime	lime )
( display FLASH2_dummyj	blank	solid	lime	lime )
( display M5_teste	X	thickLine2	winColor3	winColor3 )
( display FINFET_dummy7	hLine	solid	lime	lime )
( display M6_dummy5	X	thickLine2	tan	tan )
( display M6_dummyf	X	thickLine2	tan	tan )
( display M6_NV	X	thickLine2	tan	tan )
( display FLASH2_dummyq	blank	solid	lime	lime )
( display designFlow_drawing2	solid	solid	purple	purple )
( display SRM1_dummy1	blank	solid	forest	forest )
( display VIA1_drawinga	dot4	solid	cyan	cyan )
( display HBC_TOP_drawing	blank	solid	red	red )
( display VIA1_net	X	solid	white	white )
( display FINFET_dummy9	hLine	solid	lime	lime )
( display SSTI2_hv	blank	solid	red	red )
( display hardFence_drawing	blank	solid	red	red )
( display M3_dummy9	X	thickLine2	forest	forest )
( display CBRAM_drawinga	blank	solid	red	red )
( display RES200_drawing	blank	solid	lime	lime )
( display AVTP2_drawing	blank	solid	green	green )
( display INDDMY_dummy8	blank	solid	green	green )
( display M2_dummy4	X	thickLine2	gold	gold )
( display SRM_DOD_dummy4	blank	solid	red	red )
( display FLASH2_drawing2	blank	solid	orange	orange )
( display CB2_FC_drawing	dots	solid	yellow	yellow )
( display M8_dummyj	X	thickLine2	cream	cream )
( display M9_test9	X	thickLine2	lime	lime )
( display FINFET_dummyf	hLine	solid	lime	lime )
( display CDUDMY_drawing5	cross	solid	green	green )
( display M5_dummya	X	thickLine2	winColor3	winColor3 )
( display DMEXCL_dummy2	blank	solid	orange	orange )
( display ICOVL_drawing9	cross	solid	green	green )
( display ICOVL_drawingb	cross	solid	green	green )
( display VIAEXCL_dummy8	blank	solid	violet	violet )
( display LOGO_drawing	dot3	solid	purple	purple )
( display prBoundary_label	blank	solid	purple	purple )
( display POS_drawing2	brick	dashDot	gold	gold )
( display BTSV_drawing	X	thickLine	red	red )
( display PO_lvs	X	thickLine	forest	forest )
( display M8_testh	X	thickLine2	cream	cream )
( display FLASH_dummyk	blank	solid	lime	lime )
( display R_rule_recommend	dots	solid	pink	pink )
( display DMEXCL_dummy3	blank	solid	orange	orange )
( display VTH_NS_drawing	blank	solid	white	white )
( display Cannotoccupy_boundary	blank	thickLine	red	red )
( display M3_dummyd	X	thickLine2	forest	forest )
( display FLASH1_drawingc	blank	solid	lime	lime )
( display NW_drawing2	blank	thickLine2	cream	cream )
( display VTUL_P_drawing	blank	thickLine2	gray	gray )
( display VIA1_blockage	X	solid	yellow	yellow )
( display VTH_P_drawing	blank	shortDash	yellow	yellow )
( display PADDMY_drawing	blank	solid	lime	lime )
( display M5_dummym	X	thickLine2	winColor3	winColor3 )
( display PO_test0	cross	thickLine	silver	silver )
( display M5_blockage	dot3	solid	winColor3	winColor3 )
( display M7_dummy6	X	thickLine2	lime	lime )
( display OD_test7	backSlash	solid	purple	purple )
( display VAR_drawing3	blank	solid	yellow	yellow )
( display M8_blockage	cross	solid	cream	cream )
( display M1_test0	X	thickLine2	cyan	cyan )
( display ICOVL_dummyh	cross	solid	green	green )
( display M3_text	X	thickLine2	cyan	cyan )
( display M5_test5	X	thickLine2	winColor3	winColor3 )
( display RMDMY_drawing1	brick	dashDot	cyan	cyan )
( display PM_drawing	blank	solid	white	white )
( display AVT2_drawing	blank	solid	red	red )
( display INDDMY_drawing3	blank	solid	yellow	yellow )
( display LVSDMY_dummy3	blank	dashed	green	green )
( display M8_dummy1	X	thickLine2	cream	cream )
( display FLASH2_drawing7	dot1	solid	magenta	magenta )
( display CO_testb	cross	solid	cyan	cyan )
( display LMARK_BSL	blank	solid	white	white )
( display BMZ_drawing	blank	solid	red	red )
( display OD_drain1	dagger	solid	red	red )
( display CDUDMY_test1	cross	solid	green	green )
( display HV_net	blank	thickLine	magenta	magenta )
( display POS_test2	brick	dashDot	cyan	cyan )
( display SRM_DPO_dummy7	blank	solid	blue	blue )
( display MRDMY_test9	dot4	solid	yellow	yellow )
( display M7_dummyf	X	thickLine2	lime	lime )
( display MFUSE_drawing4	dot4	solid	blue	blue )
( display VIA6_net	X	solid	white	white )
( display SRM_test2	blank	solid	forest	forest )
( display M5_dummy5	X	thickLine2	winColor3	winColor3 )
( display HBL_TOP_drawing1	blank	solid	red	red )
( display FLASH2_test4	solid	solid	gray	gray )
( display CSRDMY_drawing2	blank	solid	blue	blue )
( display DCO_drawing2	blank	solid	orange	orange )
( display RFDMY_dummyd	X	thickLine	red	red )
( display M1_dummyc	X	thickLine2	cyan	cyan )
( display PO_test9	cross	thickLine	silver	silver )
( display M7_test8	X	thickLine2	lime	lime )
( display TCLO_test4	cross	solid	blue	blue )
( display MOMDMY_test2	blank	solid	orange	orange )
( display M5_NV	X	thickLine2	winColor3	winColor3 )
( display AP_dummy5	X	thickLine2	gold	gold )
( display M6_dummyg	X	thickLine2	tan	tan )
( display M4_dummy8	X	thickLine2	slate	slate )
( display NT_N_dummya	blank	thickLine2	gray	gray )
( display MFUSE_drawing1	dot4	solid	blue	blue )
( display CTMDMY_drawing4	blank	solid	green	green )
( display UBM_dummy2	dot1	thickLine	gray	gray )
( display FLASH_drawing	blank	solid	lime	lime )
( display SCL2_drawing	blank	solid	red	red )
( display OD_testa	backSlash	solid	silver	silver )
( display SRM_dummyh	blank	solid	forest	forest )
( display INDDMY_drawing1	blank	solid	cream	cream )
( display designFlow_drawing3	solid	solid	pink	pink )
( display M5_pin	X	thickLine2	cyan	cyan )
( display VIA4_blockage	triangle	solid	white	white )
( display MRDMY_drawing7	dot4	solid	yellow	yellow )
( display RV_pin	X	thickLine	blue	blue )
( display M3_testj	X	thickLine2	forest	forest )
( display M9_dummya	X	thickLine2	lime	lime )
( display LVSDMY_dummy5	blank	dashed	green	green )
( display FET_PILD	blank	shortDash	magenta	magenta )
( display POS_test3	brick	dashDot	cyan	cyan )
( display Group_label	blank	solid	green	green )
( display M6_boundary	dot4	solid	white	white )
( display M8_dummy4	X	thickLine2	cream	cream )
( display M8_testd	X	thickLine2	cream	cream )
( display MRDMY_dummy2	dot4	solid	yellow	yellow )
( display MOMDMY_dummyc	blank	solid	orange	orange )
( display RFDMY_dummy6	X	thickLine	red	red )
( display CBRAM_drawing4	blank	solid	red	red )
( display SRM_DOD_dummy5	blank	solid	red	red )
( display SRM_DPO_dummy3	blank	solid	blue	blue )
( display FW_drawing	dots	solid	magenta	magenta )
( display PMDMY_drawing	blank	solid	blue	blue )
( display INDDMY_drawingb	blank	solid	lime	lime )
( display M9_dummyh	X	thickLine2	lime	lime )
( display MRAM_test6	X	thickLine	red	red )
( display ICOVL_dummy8	cross	solid	green	green )
( display VIA7_drawinga	dot4	solid	cyan	cyan )
( display DIODMY_dummyb	blank	solid	white	white )
( display N10V_drawing	blank	shortDash	gray	gray )
( display FLASH2_drawingb	dot2	solid	tan	tan )
( display CBD_BSL	blank	solid	white	white )
( display SRM_LOP_dummy2	blank	solid	blue	blue )
( display M6_test0	X	thickLine2	tan	tan )
( display designFlow_drawing8	solid	solid	navy	navy )
( display DSDDMY_drawing5	blank	dashed	winColor3	winColor3 )
( display SRM_DOD_dummyd	blank	solid	red	red )
( display M8_NV	X	thickLine2	cream	cream )
( display FLASH1_drawinga	blank	solid	lime	lime )
( display FLASH2_testg	blank	solid	magenta	magenta )
( display DMEXCL_dummy6	blank	solid	orange	orange )
( display pin_annotate	blank	solid	red	red )
( display designFlow_drawing	solid	solid	green	green )
( display MOMDMY_dummyh	blank	solid	orange	orange )
( display TSV_drawing	X	thickLine	red	red )
( display SRM_dummyb	blank	solid	forest	forest )
( display MRAM_test8	blank	dashed	blue	blue )
( display PW_drawing2	blank	shortDash	cream	cream )
( display OD_15_drawing	dot3	solid	purple	purple )
( display M5_testa	X	thickLine2	winColor3	winColor3 )
( display SRM_drawing6	blank	solid	blue	blue )
( display M4_test7	X	thickLine2	slate	slate )
( display CTMDMY_drawing9	blank	solid	green	green )
( display FLASH2_dummyn	blank	solid	lime	lime )
( display AP_BSL	blank	solid	magenta	magenta )
( display HVD_n_b	blank	shortDash	gray	gray )
( display TCLO_test2	cross	solid	blue	blue )
( display M6_testa	X	thickLine2	tan	tan )
( display M9_pin	X	thickLine2	cyan	cyan )
( display M6_testh	X	thickLine2	tan	tan )
( display M9_text	X	thickLine2	cyan	cyan )
( display FLASH2_drawingc	X	solid	green	green )
( display FLASH_test9	blank	solid	lime	lime )
( display CDUDMY_dummy5	cross	solid	green	green )
( display M2_dummy1	X	thickLine2	gold	gold )
( display PM_drawinge	blank	solid	white	white )
( display OTP_dummy	blank	solid	orange	orange )
( display RAM1TDMY_drawing7	blank	solid	magenta	magenta )
( display INDDMY_test0	blank	solid	green	green )
( display PO_test1	cross	thickLine	silver	silver )
( display M7_dummy5	X	thickLine2	lime	lime )
( display AP_dummya	X	thickLine2	gold	gold )
( display HV_dummy7	blank	shortDash	gray	gray )
( display CBRAM_drawingd	blank	solid	red	red )
( display TCLO_dummy5	cross	solid	cyan	cyan )
( display DSDDMY_test4	blank	dashed	winColor5	winColor5 )
( display MRDMY_dummyq	blank	solid	navy	navy )
( display R_rule_guideline	dots	solid	green	green )
( display FLASH_drawinge	blank	solid	lime	lime )
( display FLASH1_dummyb	blank	solid	lime	lime )
( display M5_boundary	dot4	solid	white	white )
( display M2_dummy	X	thickLine2	gold	gold )
( display MRDMY_test8	dot4	solid	yellow	yellow )
( display FLASH1_dummyl	blank	solid	lime	lime )
( display NW_NSC4_drawing	blank	shortDash	cream	cream )
( display M3_dummy8	X	thickLine2	forest	forest )
( display M2_dummyi	X	thickLine2	gold	gold )
( display VIA3_dummy	vLine	solid	purple	purple )
( display chipBoundary_drawing	blank	solid	red	red )
( display MOMDMY_dummyg	blank	solid	orange	orange )
( display SOI_PLDDV	dots	solid	gray	gray )
( display annotate_drawing8	blank	solid	tan	tan )
( display PSUB2_drawing	X	solid	white	white )
( display ICOVL_drawingc	cross	solid	green	green )
( display SRM1_dummy3	blank	solid	forest	forest )
( display DIODMY_dummyc	blank	solid	white	white )
( display PO_test8	cross	thickLine	silver	silver )
( display PO_test7	cross	thickLine	silver	silver )
( display M5_dummyj	X	thickLine2	winColor3	winColor3 )
( display M7_test0	X	thickLine2	lime	lime )
( display SRM_dummyk	blank	solid	forest	forest )
( display M1_testb	X	thickLine2	cyan	cyan )
( display M8_test2	X	thickLine2	cream	cream )
( display FLASH2_test3	dots	solid	magenta	magenta )
( display M6_pin	X	thickLine2	cyan	cyan )
( display M8_dummyf	X	thickLine2	cream	cream )
( display PO_testc	cross	thickLine	tan	tan )
( display OD_blockage	blank	thickLine2	green	green )
( display DVIAEXCL_dummy5	blank	solid	orange	orange )
( display M2_test8	X	thickLine2	gold	gold )
( display RFDMY_test0	X	thickLine	red	red )
( display M1_test2	X	thickLine2	cyan	cyan )
( display M8_test0	X	thickLine2	cream	cream )
( display AP_test3	dots	solid	gray	gray )
( display RPDMY_dummy1	brick	dashDot	pink	pink )
( display TCLO_dummya	cross	solid	cyan	cyan )
( display M1_dummyg	X	thickLine2	cyan	cyan )
( display M7_testb	X	thickLine2	lime	lime )
( display DSDDMY_test3	blank	dashed	winColor5	winColor5 )
( display SRM_test3	blank	solid	forest	forest )
( display FLASH2_dummye	blank	solid	blue	blue )
( display MRDMY_dummyg	dot4	solid	yellow	yellow )
( display hilite_drawing7	blank	solid	cream	cream )
( display RFDMY_dummyf	X	thickLine	red	red )
( display NW55_drawing	blank	solid	orange	orange )
( display SRM_dummy7	blank	solid	forest	forest )
( display BTSV_drawing3	X	thickLine	red	red )
( display FLASH1_drawing8	blank	solid	lime	lime )
( display BJTDMY_dummy3	triangle	solid	green	green )
( display CO_test7	cross	solid	blue	blue )
( display OD_test6	backSlash	solid	purple	purple )
( display VIA6_pin	solid	solid	yellow	yellow )
( display SRM_dummya	blank	solid	forest	forest )
( display DVIAEXCL_dummyb	blank	solid	orange	orange )
( display M3_odummy	dot4	thickLine2	forest	forest )
( display FLASH1_drawinge	blank	solid	lime	lime )
( display FLASH_dummyi	blank	solid	lime	lime )
( display M6_dummyb	X	thickLine2	tan	tan )
( display M1_dummy	X	thickLine2	cyan	cyan )
( display SRM_dummy4	blank	solid	forest	forest )
( display ICOVL_dummyj	cross	solid	green	green )
( display VIA5_drawing	slash	solid	cream	cream )
( display HVCORE_well	blank	solid	red	red )
( display FLASH1_drawingh	blank	solid	lime	lime )
( display pin_drawing	solid	solid	red	red )
( display VTLN_N25_drawing	blank	solid	red	red )
( display M1_odummy	dot4	thickLine2	cyan	cyan )
( display FLASH2_drawing3	blank	solid	green	green )
( display M1_test7	X	thickLine2	cyan	cyan )
( display RFDMY_test4	X	thickLine	red	red )
( display ICOVL_testc	cross	solid	green	green )
( display FLASH_drawing9	blank	solid	lime	lime )
( display M3_dummyh	X	thickLine2	forest	forest )
( display device_drawing2	blank	dashed	green	green )
( display VIAEXCL_drawing	blank	solid	violet	violet )
( display RFDMY_drawingh	X	thickLine	red	red )
( display UHVT_N_drawing	blank	solid	navy	navy )
( display device_label	blank	solid	green	green )
( display CDUDMY_test3	cross	solid	green	green )
( display MRDMY_drawingf	dot4	solid	yellow	yellow )
( display MRAM_drawing7	blank	solid	navy	navy )
( display TCLO_dummy4	cross	solid	cyan	cyan )
( display M3_dummya	X	thickLine2	forest	forest )
( display M7_dummy8	X	thickLine2	lime	lime )
( display VIA7_grid	vLine	solid	tan	tan )
( display MOMDMY_drawing8	blank	dashed	cyan	cyan )
( display VIA6_dummy	x	solid	gold	gold )
( display RMDMY_drawing3	brick	dashDot	forest	forest )
( display SRM1_dummyg	blank	solid	forest	forest )
( display RFDMY_drawing4	X	thickLine	red	red )
( display M5_test3	X	thickLine2	winColor3	winColor3 )
( display VIA2_drawing	hLine	solid	yellow	yellow )
( display y9_drawing	blank	dashed	silverB	silverB )
( display M5_test7	X	thickLine2	winColor3	winColor3 )
( display R_rule_analog	dots	solid	orange	orange )
( display MRDMY_test4	dot4	solid	yellow	yellow )
( display HV_dummy9	blank	thickLine	magenta	magenta )
( display M8_dummyl	X	thickLine2	cream	cream )
( display SRAMDMY_drawing1	solid	solid	orange	orange )
( display DVIAEXCL_dummy8	blank	solid	orange	orange )
( display PODE_blocking	blank	solid	red	red )
( display OD_25_udrdrv	cross	thickLine	winColor2	winColor1 )
( display M8_testi	X	thickLine2	cream	cream )
( display MRAM_drawingd	dot4	solid	yellow	yellow )
( display PM_dummy	blank	solid	white	white )
( display VIA8_net	X	solid	white	white )
( display M4_pin	X	thickLine2	cyan	cyan )
( display M9_dummy3	X	thickLine2	lime	lime )
( display SRM1_dummyj	blank	solid	forest	forest )
( display WAIVER_drawing5	blank	solid	red	red )
( display WAIVER_drawing8	blank	solid	red	red )
( display y8_drawing	blank	dashed	goldB	goldB )
( display M1_testa	X	thickLine2	cyan	cyan )
( display RV_drawingb	x	solid	forest	forest )
( display SRM_LOP_dummy8	blank	solid	tan	tan )
( display CELL_M_drawing	blank	solid	red	red )
( display VIA2_boundary	blank	solid	white	white )
( display AP_drawingd	dots	solid	gray	gray )
( display TCLO_testa	cross	solid	cyan	cyan )
( display M4_dummy3	X	thickLine2	slate	slate )
( display M8_dummye	X	thickLine2	cream	cream )
( display M9_testc	X	thickLine2	lime	lime )
( display MOMDMY_drawingd	blank	dashed	forest	forest )
( display TFRDUMMY_6_drawing	X	thickLine	red	red )
( display ICOVL_drawing4	cross	solid	green	green )
( display M3_drawing	dot4	solid	forest	forest )
( display OD_25_drawing	cross	thickLine	winColor2	winColor1 )
( display SRM1_dummy5	blank	solid	forest	forest )
( display M7_dummy3	X	thickLine2	lime	lime )
( display FLASH1_drawing5	blank	solid	lime	lime )
( display M3_dummy3	X	thickLine2	forest	forest )
( display M9_test4	X	thickLine2	lime	lime )
( display AP_prob	dot4	thickLine2	gold	gold )
( display DMEXCL_dummy7	blank	solid	orange	orange )
( display M7_net	X	solid	white	white )
( display M6_blockage	stipple3	solid	tan	tan )
( display MOMDMY_drawing3	blank	solid	cyan	cyan )
( display M6_test1	X	thickLine2	tan	tan )
( display SSTI_hv	blank	solid	red	red )
( display annotate_drawing5	blank	solid	white	white )
( display M3_test1	X	thickLine2	forest	forest )
( display FLASH2_dummyl	blank	solid	lime	lime )
( display INDDMY_drawing4	blank	solid	cream	cream )
( display RMDMY_drawing9	brick	dashDot	winColor5	winColor5 )
( display RFDMY_drawing7	X	thickLine	red	red )
( display MRAM_test4	dot4	solid	yellow	yellow )
( display M2_dummye	X	thickLine2	gold	gold )
( display SRM_DOD_dummy9	blank	solid	red	red )
( display SRM_testc	blank	solid	gold	gold )
( display M7_dummyk	X	thickLine2	lime	lime )
( display TCLO_test9	cross	solid	blue	blue )
( display DSDDMY_test8	blank	dashed	winColor5	winColor5 )
( display M5_dummy1	X	thickLine2	winColor3	winColor3 )
( display FET_NILD	blank	thickLine	magenta	magenta )
( display CDUDMY_drawing6	cross	solid	green	green )
( display AP_blockage	dots	solid	gray	gray )
( display SOI_PLDDH	dots	solid	magenta	magenta )
( display ODBLK_dummy	blank	solid	orange	orange )
( display VIA7_boundary	blank	solid	white	white )
( display VIA5_boundary	blank	solid	white	white )
( display SRM_drawing9	blank	solid	blue	blue )
( display CAP_IMP_drawing	blank	solid	red	red )
( display M2_dummy5	X	thickLine2	gold	gold )
( display PDK_drawing	blank	dashed	purple	purple )
( display VIA7_drawing	vLine	solid	tan	tan )
( display LVSDMY_dummy2	blank	dashed	green	green )
( display text_drawing	blank	solid	white	white )
( display ESD3_drawing	blank	solid	navy	navy )
( display ICOVL_dummy1	cross	solid	green	green )
( display CTM_drawing2	triangle	solid	purple	purple )
( display MOMDMY_hv	blank	solid	orange	orange )
( display OD_pin	X	thickLine	blue	blue )
( display OD_testc	backSlash	solid	silver	silver )
( display ICOVL_drawingh	cross	solid	green	green )
( display RV_drawing2	x	solid	forest	forest )
( display M2_odummy	dot4	thickLine2	gold	gold )
( display Unrouted_drawing1	blank	dashed	brown	brown )
( display M1_boundary	dot4	solid	white	white )
( display M2_dummyc	X	thickLine2	gold	gold )
( display M4_teste	X	thickLine2	slate	slate )
( display VAR_drawing1	blank	solid	yellow	yellow )
( display pin_label	blank	solid	red	red )
( display FLASH_drawing5	blank	solid	lime	lime )
( display VDDDMY_drawing	blank	solid	navy	navy )
( display M4_boundary	dot4	solid	white	white )
( display MRDMY_drawing	X	thickLine	red	red )
( display M4_test1	X	thickLine2	slate	slate )
( display VIA4_dummy	triangle	solid	white	white )
( display MRDMY_drawing8	dot4	solid	yellow	yellow )
( display changedLayer_tool0	blank	solid	red	red )
( display RPO_drawing1	blank	solid	forest	forest )
( display SCL_drawing	blank	solid	red	red )
( display M1_text	X	thickLine2	cyan	cyan )
( display DIO3TDMY_drawing	blank	solid	white	white )
( display FET_p_b	dots	solid	magenta	magenta )
( display VIAEXCL_dummy3	blank	solid	violet	violet )
( display y3_drawing	blank	dashed	pinkB	pinkB )
( display VIA5_pin	solid	solid	yellow	yellow )
( display prBoundary_drawing	blank	solid	purple	purple )
( display RV_net	X	solid	lime	lime )
( display eVTL_P_drawing	blank	thickLine2	blue	blue )
( display SRM_drawing	blank	solid	tan	tan )
( display CO_test5	cross	solid	blue	blue )
( display HBL_BOT_drawing	blank	solid	red	red )
( display CO_test2	cross	solid	blue	blue )
( display MOMDMY_dummy4	blank	solid	red	red )
( display LUPWDMY_drawing	blank	solid	navy	navy )
( display FLASH1_drawing2	blank	solid	lime	lime )
( display CO_test9	cross	solid	blue	blue )
( display MRAM_drawinge	blank	solid	navy	navy )
( display DVIAEXCL_dummyc	blank	solid	orange	orange )
( display M5_dummyi	X	thickLine2	winColor3	winColor3 )
( display DIODMY_drawing	blank	solid	white	white )
( display CTMDMY_drawing7	blank	solid	green	green )
( display VIA3_pin	solid	solid	yellow	yellow )
( display MRDMY_dummye	dot4	solid	yellow	yellow )
( display M1_test4	X	thickLine2	cyan	cyan )
( display FLASH2_drawinge	blank	thickLine2	tan	tan )
( display designFlow_drawing6	solid	solid	tan	tan )
( display M1_dummye	X	thickLine2	cyan	cyan )
( display annotate_drawing1	blank	solid	pink	pink )
( display WRV_drawing	x	solid	forest	forest )
( display INDDMY_dummya	blank	solid	green	green )
( display PW_drawing	blank	shortDash	cream	cream )
( display INDDMY_test2	blank	solid	cyan	cyan )
( display RMDMY_drawingb	brick	dashDot	orange	orange )
( display OD_25_v_15	u	solid	winColor3	winColor3 )
( display MRDMY_dummyo	dot4	solid	yellow	yellow )
( display TCLO_dummy2	cross	solid	cyan	cyan )
( display CDUDMY_dummy3	cross	solid	green	green )
( display M1_grid	cross	solid	cyan	cyan )
( display CB2_testa	blank	solid	white	white )
( display M7_boundary	dot4	solid	white	white )
( display ICOVL_drawinga	cross	solid	green	green )
( display M6_test7	X	thickLine2	tan	tan )
( display PM_drawingc	blank	solid	white	white )
( display INDDMY_dummy6	blank	solid	cyan	cyan )
( display text_drawing1	blank	dashed	white	white )
( display marker_error	X	solid	white	whiteB )
( display RAM1TDMY_drawing5	blank	solid	magenta	magenta )
( display M8_dummyd	X	thickLine2	cream	cream )
( display M4_odummy	dot4	thickLine2	slate	slate )
( display SRM_test1	blank	solid	forest	forest )
( display PSUB_hv	blank	solid	red	red )
( display MOMDMY_dummya	blank	solid	orange	orange )
( display HV_dummy5	blank	thickLine	magenta	magenta )
( display TCLO_dummy7	cross	solid	cyan	cyan )
( display M5_testc	X	thickLine2	winColor3	winColor3 )
( display Unrouted_drawing	blank	dashed	winColor5	winColor5 )
( display M2_dummyj	X	thickLine2	gold	gold )
( display OD25_NSC1_drawing	u	solid	winColor3	winColor3 )
( display LDDBLK_drawingb	blank	solid	orange	orange )
( display M2_dummy3	X	thickLine2	gold	gold )
( display CBRAM_drawingc	blank	solid	red	red )
( display M3_grid	dot4	solid	forest	forest )
( display PO65_drawing	blank	solid	green	green )
( display text_drawing2	solid	solid	white	white )
( display FLASH1_dummyr	blank	solid	lime	lime )
( display SRM_test8	blank	solid	forest	forest )
( display FLASH2_dummyp	blank	solid	lime	lime )
( display M5_dummyd	X	thickLine2	winColor3	winColor3 )
( display BTSV_drawing1	X	thickLine	red	red )
( display M1_dummyi	X	thickLine2	cyan	cyan )
( display M4_dummyd	X	thickLine2	slate	slate )
( display FLASH_test7	blank	solid	lime	lime )
( display DSDDMY_test6	blank	dashed	winColor5	winColor5 )
( display M1_testd	X	thickLine2	cyan	cyan )
( display TFR_drawing	blank	solid	white	white )
( display VIA5_dummy	slash	solid	cream	cream )
( display resist_drawing	blank	solid	cyan	cyan )
( display M7_teste	X	thickLine2	lime	lime )
( display FLASH2_test8	blank	solid	tan	tan )
( display BMZ2_dummy1	blank	solid	red	red )
( display M3_dummyf	X	thickLine2	forest	forest )
( display M5_prob	dot4	thickLine2	cyan	cyan )
( display DSDDMY_drawing2	blank	dashed	gold	gold )
( display RAM_drawing1	blank	solid	navy	navy )
( display DVIAEXCL_dummy3	blank	solid	orange	orange )
( display M4_dummyf	X	thickLine2	slate	slate )
( display AP_drawingb	dots	solid	gray	gray )
( display DMEXCL_dummyd	blank	solid	orange	orange )
( display M7_test4	X	thickLine2	lime	lime )
( display DSDDMY_drawing7	blank	dashed	lime	lime )
( display AP_dummyc	X	thickLine2	gold	gold )
( display FLASH_drawing7	blank	solid	lime	lime )
( display MOMDMY_test8	blank	solid	orange	orange )
( display FLASH2_drawing5	blank	solid	pink	pink )
( display RFDMY_drawingf	blank	dashed	purple	purple )
( display FLASH_dummyf	blank	solid	lime	lime )
( display wire_flight	blank	dashed	red	red )
( display SOI_MKR_decap	cross	solid	green	green )
( display RMDMY_drawing5	brick	dashDot	winColor3	winColor3 )
( display CB2_BSL	blank	solid	green	green )
( display M5_text	X	thickLine2	cyan	cyan )
( display HVCORE_nldds	blank	solid	red	red )
( display M3_dummyc	X	thickLine2	forest	forest )
( display M4_VNV	X	thickLine2	slate	slate )
( display SRAMDMY_passgate	blank	solid	red	red )
( display AP_drawing4	dots	solid	gray	gray )
( display EHVT_N_drawing	blank	thickLine	magenta	magenta )
( display RAM1TDMY_drawing3	blank	solid	magenta	magenta )
( display M5_test1	X	thickLine2	winColor3	winColor3 )
( display FLASH_drawing1	blank	solid	lime	lime )
( display M2_dummy6	X	thickLine2	gold	gold )
( display HVD_p_a	blank	shortDash	magenta	magenta )
( display hilite_drawing	blank	solid	white	white )
( display SEALRING_dummya	blank	solid	gold	gold )
( display stretch_drawing	blank	solid	yellow	yellow )
( display SRM_DOD_dummy7	blank	solid	red	red )
( display M7_test5	X	thickLine2	lime	lime )
( display MRDMY_dummyj	dot4	solid	yellow	yellow )
( display FLASH2_test1	blank	shortDash	gray	gray )
( display VIA8_pin	solid	solid	yellow	yellow )
( display RMDMY_drawingh	brick	dashDot	gold	gold )
( display CSRDMY_drawing1	blank	solid	blue	blue )
( display M8_test4	X	thickLine2	cream	cream )
( display FLASH_dummyh	blank	solid	lime	lime )
( display HBL_BOT_dummy1	blank	solid	red	red )
( display HVD_drawing2	blank	shortDash	magenta	magenta )
( display HV_dummyn	blank	shortDash	gray	gray )
( display ICOVL_dummyc	cross	solid	green	green )
( display VIA3_drawing	vLine	solid	purple	purple )
( display M1_dummy2	X	thickLine2	cyan	cyan )
( display CTMDMY_drawing	blank	solid	green	green )
( display INDDMY_dummy4	blank	solid	yellow	yellow )
( display SRM_DOD_dummyb	blank	solid	red	red )
( display SRM_DOD_dummy1	blank	solid	red	red )
( display SRAMDMY_dummy8	solid	solid	orange	orange )
( display M6_dummyd	X	thickLine2	tan	tan )
( display M4_dummy1	X	thickLine2	slate	slate )
( display SRM_DOD_dummy8	blank	solid	red	red )
( display M3_dummy5	X	thickLine2	forest	forest )
( display y1_drawing	blank	dashed	brownB	brownB )
( display CB2_drawing1	blank	solid	white	white )
( display M8_dummyh	X	thickLine2	cream	cream )
( display M6_dummy7	X	thickLine2	tan	tan )
( display SRM1_dummye	blank	solid	forest	forest )
( display VIA3_blockage	vLine	solid	purple	purple )
( display FINFET_dummyh	hLine	solid	lime	lime )
( display ODRZ_dummy	blank	solid	orange	orange )
( display PO_test5	cross	thickLine	silver	silver )
( display M7_dummym	X	thickLine2	lime	lime )
( display M3_testa	X	thickLine2	forest	forest )
( display FLASH_test1	blank	solid	lime	lime )
( display SRAMDMY_cvss_sram	blank	solid	pink	pink )
( display RPDMY_drawing	brick	dashDot	winColor4	winColor4 )
( display TCDDMY_drawing2	cross	solid	green	green )
( display DECAPDMY_drawing	blank	dashed	blue	blue )
( display FLASH2_dummyg	blank	solid	lime	lime )
( display M9_dummy	X	thickLine2	lime	lime )
( display CO2_drawing	blank	solid	silver	silver )
( display SRM_drawingd	blank	solid	tan	tan )
( display FLASH1_dummye	blank	solid	lime	lime )
( display CDUDMY_test5	cross	solid	green	green )
( display BMZ_pin	blank	solid	red	red )
( display VT_N_drawing1	dot4	thickLine2	cyan	cyan )
( display RFDMY_dummy3	X	thickLine	red	red )
( display M9_prob	dot4	thickLine2	cyan	cyan )
( display RV_grid	x	solid	forest	forest )
( display FINFET_dummyi	hLine	solid	lime	lime )
( display M2_test3	X	thickLine2	gold	gold )
( display SRM_dummyf	blank	solid	forest	forest )
( display MOMDMY_drawing1	blank	solid	red	red )
( display INDDMY_drawinge	blank	solid	lime	lime )
( display annotate_drawing3	blank	solid	cyan	cyan )
( display M1_testi	X	thickLine2	cyan	cyan )
( display ICOVL_testa	cross	solid	green	green )
( display RFDMY_test2	X	thickLine	red	red )
( display PDK_dummy	blank	dashed	purple	purple )
( display hilite_drawing5	blank	solid	lime	lime )
( display OD_25_LOWLK	u	solid	winColor3	winColor3 )
( display MRDMY_drawingh	dot4	solid	yellow	yellow )
( display DMEXCL_dummy9	blank	solid	orange	orange )
( display RRAM_dummy1	blank	solid	red	red )
( display M7_dummyb	X	thickLine2	lime	lime )
( display VTL_P_drawing	blank	thickLine2	gray	gray )
( display PM_drawing1	blank	solid	white	white )
( display CTMDMY_drawinga	blank	solid	green	green )
( display INDDMY_dummy2	blank	solid	cyan	cyan )
( display ICOVL_test1	cross	solid	green	green )
( display CDUDMY_drawing1	cross	solid	green	green )
( display TGO_EHVT_drawing	blank	solid	red	red )
( display MOMDMY_test5	blank	solid	orange	orange )
( display HVIO_nldds	blank	solid	red	red )
( display M6_text	X	thickLine2	cyan	cyan )
( display PO_testa	cross	thickLine	tan	tan )
( display M9_testj	X	thickLine2	lime	lime )
( display M8_test8	X	thickLine2	cream	cream )
( display DGATE_drawing1	blank	solid	red	red )
( display M3_boundary	dot4	solid	white	white )
( display M7_dummye	X	thickLine2	lime	lime )
( display M1_test8	X	thickLine2	cyan	cyan )
( display TCDDMY_drawing7	cross	solid	green	green )
( display M3_dummy4	X	thickLine2	forest	forest )
( display SRM_DOD_dummyc	blank	solid	red	red )
( display M1_dummym	X	thickLine2	cyan	cyan )
( display MRAM_test2	brick	dashDot	blue	blue )
( display M6_testd	X	thickLine2	tan	tan )
( display CBRAM_drawing	blank	solid	red	red )
( display MOMDMY_dummyd	blank	solid	orange	orange )
( display HVD_33_drawing	blank	thickLine	magenta	magenta )
( display CTMDMY_drawing8	blank	solid	green	green )
( display HCDMY_drawing	blank	solid	red	red )
( display RRAM_dummy6	blank	solid	red	red )
( display MOMDMY_drawing4	blank	solid	orange	orange )
( display CDUDMY_dummy1	cross	solid	green	green )
( display MOMDMY_testa	blank	solid	orange	orange )
( display background_drawing	solid	solid	black	black )
( display M3_dummye	X	thickLine2	forest	forest )
( display HVIO_pldds	blank	solid	red	red )
( display VIA5_drawinga	dot4	solid	cyan	cyan )
( display FLASH2_testc	blank	solid	magenta	magenta )
( display MRDMY_dummym	dot4	solid	yellow	yellow )
( display DCO_drawing	blank	solid	orange	orange )
( display select_drawing	blank	solid	tan	tan )
( display FLASH2_test7	blank	solid	forest	forest )
( display WAIVER_drawing3	blank	solid	red	red )
( display M9_VNV	X	thickLine2	lime	lime )
( display SEALRING_drawing5	blank	solid	gold	gold )
( display SRM_dummyl	blank	solid	forest	forest )
( display HV_dummy6	blank	thickLine	magenta	magenta )
( display M6_dummy2	X	thickLine2	tan	tan )
( display BJTDMY_dummy5	triangle	solid	green	green )
( display PMET_CUT_drawing1	blank	solid	white	white )
( display FLASH1_dummyp	blank	solid	lime	lime )
( display MOMDMY_drawing6	blank	solid	cream	cream )
( display RFDMY_drawing2	X	thickLine	red	red )
( display SEALRING_drawinga	blank	solid	gold	gold )
( display M4_testj	X	thickLine2	slate	slate )
( display ICOVL_dummy3	cross	solid	green	green )
( display FLASH2_dummyd	blank	solid	red	red )
( display SRM_drawinga	blank	solid	tan	tan )
( display FLASH_dummyc	blank	solid	lime	lime )
( display FLASH_drawingb	blank	solid	lime	lime )
( display FLASH2_drawing6	dots	solid	magenta	magenta )
( display device_drawing	blank	solid	green	green )
( display VIA1_dummy	X	solid	yellow	yellow )
( display CDUDMY_drawing2	cross	solid	green	green )
( display CPO_BLOCK_drawing	blank	solid	yellow	yellow )
( display M8_dummya	X	thickLine2	cream	cream )
( display SRM_DOD_dummy2	blank	solid	red	red )
( display POS_dummyb	brick	dashDot	cyan	cyan )
( display OD_25_v_12	u	solid	winColor3	winColor3 )
( display M5_testi	X	thickLine2	winColor3	winColor3 )
( display FLASH2_dummyi	blank	solid	lime	lime )
( display device_annotate	blank	solid	yellow	yellow )
( display PO_test4	cross	thickLine	silver	silver )
( display POS_plus2	brick	dashDot	gold	gold )
( display M3_test8	X	thickLine2	forest	forest )
( display M7_test1	X	thickLine2	lime	lime )
( display MOMDMY_drawingc	blank	dashed	forest	forest )
( display FLASH_test2	blank	solid	lime	lime )
( display INDDMY_drawing2	blank	solid	lime	lime )
( display CO_drawinga	cross	solid	cyan	cyan )
( display VIAEXCL_dummy5	blank	solid	violet	violet )
( display SUPER_AVT_drawing	blank	solid	red	red )
( display TANLCDMY_drawing	blank	solid	red	red )
( display VIA0_drawing	dot3	solid	red	red )
( display PO_test3	cross	thickLine	silver	silver )
( display RFDMY_dummy2	X	thickLine	red	red )
( display M6_test5	X	thickLine2	tan	tan )
( display M3_dummyj	X	thickLine2	forest	forest )
( display ref_drawing	blank	hidden	red	red )
( display RFDMY_drawing1	X	thickLine	red	red )
( display ICOVL_drawing6	cross	solid	green	green )
( display hilite_drawing9	blank	solid	pink	pink )
( display M7_VNV	X	thickLine2	lime	lime )
( display FLASH_testa	blank	solid	lime	lime )
( display OD_25_v_28	u	solid	winColor3	winColor3 )
( display M4_text	X	thickLine2	cyan	cyan )
( display SRAMDMY_waive	blank	solid	blue	blue )
( display FLASH1_drawing9	blank	solid	lime	lime )
( display SR_ESD_drawing	X	thickLine2	lime	lime )
( display VIA6_drawing	x	solid	gold	gold )
( display BDCT_drawing	blank	solid	red	red )
( display LUPWDMY_drawing1	blank	solid	navy	navy )
( display y2_drawing	blank	dashed	redB	redB )
( display LVSDMY_dummy4	blank	dashed	green	green )
( display FLASH_test4	blank	solid	lime	lime )
( display VTL_N25_S_drawing	blank	solid	orange	orange )
( display MRDMY_dummyt	blank	thickLine	magenta	magenta )
( display SOI_ndop	blank	shortDash	gray	gray )
( display M8_dummyb	X	thickLine2	cream	cream )
( display SOI_pdop	blank	shortDash	magenta	magenta )
( display FLGT_drawing	solid	solid	orange	orange )
( display FINFET_dummyb	hLine	solid	lime	lime )
( display MFUSE_drawing5	dot4	solid	blue	blue )
( display M1_dummy5	X	thickLine2	cyan	cyan )
( display FLASH2_test5	blank	solid	lime	lime )
( display MOMDMY_teste	blank	solid	orange	orange )
( display M4_drawing	dot4	solid	slate	slate )
( display M4_testc	X	thickLine2	slate	slate )
( display SRM_DPO_dummy6	blank	solid	blue	blue )
( display M1_dummyb	X	thickLine2	cyan	cyan )
( display FINFET_dummyd	hLine	solid	lime	lime )
( display Unrouted_drawing7	blank	dashed	purple	purple )
( display VIA1_boundary	blank	solid	white	white )
( display ESD3_drawing1	blank	solid	navy	navy )
( display M9_test0	X	thickLine2	lime	lime )
( display M4_test3	X	thickLine2	slate	slate )
( display SRM1_dummyb	blank	solid	forest	forest )
( display HV_ndd	blank	thickLine	magenta	magenta )
( display OD_test4	backSlash	solid	purple	purple )
( display PP_NSC2_drawing	blank	solid	magenta	magenta )
( display INDDMY_test4	blank	solid	purple	purple )
( display M9_dummy6	X	thickLine2	lime	lime )
( display SDI_drawing	blank	solid	white	white )
( display RROPC_BLK_drawing	blank	solid	red	red )
( display SRM_LOP_dummy5	blank	solid	tan	tan )
( display INDDMY_drawing	blank	solid	yellow	yellow )
( display M5_dummy4	X	thickLine2	winColor3	winColor3 )
( display VIA4_drawing	triangle	solid	white	white )
( display PM_drawingb	blank	solid	white	white )
( display M5_test8	X	thickLine2	winColor3	winColor3 )
( display MRDMY_dummyd	dot4	solid	yellow	yellow )
( display M7_dummyj	X	thickLine2	lime	lime )
( display unknown_drawing	blank	solid	yellow	yellow )
( display M4_dummym	X	thickLine2	slate	slate )
( display CTM_dummy1	triangle	solid	purple	purple )
( display CAPDMY_drawing1	blank	solid	red	red )
( display RMDMY_drawinga	brick	dashDot	orange	orange )
( display M5_dummyc	X	thickLine2	winColor3	winColor3 )
( display M6_test4	X	thickLine2	tan	tan )
( display EODDMY_hv	blank	solid	red	red )
( display M1_dummy7	X	thickLine2	cyan	cyan )
( display M9_dummye	X	thickLine2	lime	lime )
( display CO_test3	cross	solid	blue	blue )
( display OD25_BLK_drawing	blank	solid	orange	orange )
( display FLASH_drawing3	blank	solid	lime	lime )
( display M4_testa	X	thickLine2	slate	slate )
( display CELLBLK_drawing	blank	solid	red	red )
( display DSDDMY_drawing9	blank	dashed	winColor5	winColor5 )
( display M5_dummyf	X	thickLine2	winColor3	winColor3 )
( display RH_drawing	dagger	dashed	pink	pink )
( display M6_dummyk	X	thickLine2	tan	tan )
( display MRDMY_drawing9	dot4	solid	yellow	yellow )
( display PM_drawing3	blank	solid	white	white )
( display FLASH1_drawingd	blank	solid	lime	lime )
( display HVD_udrdrv	blank	shortDash	magenta	magenta )
( display wire_drawing	solid	solid	gray	gray )
( display M2_dummyg	X	thickLine2	gold	gold )
( display RAM_drawing2	blank	solid	navy	navy )
( display DSDDMY_test9	blank	dashed	winColor5	winColor5 )
( display DIODMY_dummye	blank	solid	white	white )
( display AP_drawing	dots	solid	gray	gray )
( display TCLO_test8	cross	solid	blue	blue )
( display CDUDMY_dummy4	cross	solid	green	green )
( display CDUDMY_drawing8	cross	solid	green	green )
( display VAR_drawing2	blank	solid	yellow	yellow )
( display boundary_boundary	blank	dashed	red	red )
( display HVNW_drawing	blank	thickLine2	red	red )
( display OD_net	X	solid	lime	lime )
( display CO_test8	cross	solid	blue	blue )
( display IONW_LV_drawing	cross	dashed	white	white )
( display RFDMY_drawing	blank	solid	white	white )
( display VIA2_dummy	hLine	solid	yellow	yellow )
( display designFlow_drawing5	solid	solid	silver	silver )
( display INDDMY_drawing7	blank	solid	cream	cream )
( display FLASH1_dummyh	blank	solid	lime	lime )
( display M5_net	X	solid	white	white )
( display M3_pin	X	thickLine2	cyan	cyan )
( display CBRAM_drawing7	blank	solid	red	red )
( display FET_L01S	dot1	solid	magenta	magenta )
( display MRDMY_dummy5	dot4	solid	yellow	yellow )
( display M7_testc	X	thickLine2	lime	lime )
( display M9_test7	X	thickLine2	lime	lime )
( display M7_testi	X	thickLine2	lime	lime )
( display MRDMY_drawing5	dot4	solid	yellow	yellow )
( display M2_NV	X	thickLine2	gold	gold )
( display FLASH1_dummyg	blank	solid	lime	lime )
( display AP_dummy1	X	thickLine2	gold	gold )
( display Unrouted_drawing5	blank	dashed	green	green )
( display MOMDMY_test3	blank	solid	orange	orange )
( display FINFET_dummy8	hLine	solid	lime	lime )
( display BVZ2_drawing	blank	solid	red	red )
( display DVIAEXCL_dummy1	blank	solid	orange	orange )
( display IMSOR4_dummy5	blank	solid	red	red )
( display M7_odummy	dot4	thickLine2	lime	lime )
( display y7_drawing	blank	dashed	purpleB	purpleB )
( display HBL_BLK_drawing	blank	solid	red	red )
( display SOI_MKR_gated	triangle	solid	green	green )
( display DVIAEXCL_dummy4	blank	solid	orange	orange )
( display M6_dummyi	X	thickLine2	tan	tan )
( display TCDDMY_drawing3	cross	solid	green	green )
( display CB2_drawinga	blank	solid	white	white )
( display DPDMY_drawing	blank	solid	navy	navy )
( display DSDDMY_drawing4	blank	dashed	slate	slate )
( display CO2_drawing1	blank	solid	silver	silver )
( display SIOTP_drawing	blank	thickLine2	cream	cream )
( display IODMY_dummy1	dot4	solid	yellow	yellow )
( display M2_testj	X	thickLine2	gold	gold )
( display VIAEXCL_dummyb	blank	solid	violet	violet )
( display SENDMY_drawingb	blank	solid	navy	navy )
( display VIAEXCL_dummy9	blank	solid	violet	violet )
( display ICOVL_drawing1	cross	solid	green	green )
( display M2_test9	X	thickLine2	gold	gold )
( display IMSOR4_dummy4	blank	solid	red	red )
( display MOMDMY_dummy1	blank	dashed	slate	slate )
( display hiz_drawing	blank	solid	orange	orange )
( display VTH_N_drawing	blank	thickLine2	green	green )
( display FLASH_dummym	blank	solid	lime	lime )
( display VIA4_grid	triangle	solid	white	white )
( display SRM1_dummy2	blank	solid	forest	forest )
( display HBL_TOP_dummy1	blank	solid	red	red )
( display MRDMY_drawingb	dot4	solid	yellow	yellow )
( display spike_drawing	blank	solid	purple	purple )
( display FLASH_testc	blank	solid	lime	lime )
( display M9_dummy8	X	thickLine2	lime	lime )
( display PO55_drawing	blank	solid	red	red )
( display SRM_LOP_dummy4	blank	solid	tan	tan )
( display FINFET_dummy6	hLine	solid	lime	lime )
( display RFDMY_dummy9	X	thickLine	red	red )
( display M5_dummy8	X	thickLine2	winColor3	winColor3 )
( display M8_dummy7	X	thickLine2	cream	cream )
( display M4_dummy7	X	thickLine2	slate	slate )
( display M3_dummyg	X	thickLine2	forest	forest )
( display M9_dummyk	X	thickLine2	lime	lime )
( display FLASH2_testa	blank	solid	magenta	magenta )
( display VTPCEL_drawing	blank	solid	magenta	magenta )
( display M8_odummy	brick	dashDot	cream	cream )
( display SRM_drawingc	blank	solid	tan	tan )
( display TFRSERPDMY_drawing	blank	solid	orange	orange )
( display M3_test4	X	thickLine2	forest	forest )
( display POFUSE_drawing2	dot4	solid	blue	blue )
( display VIA8_blockage	stipple3	solid	gold	gold )
( display MRDMY_test2	dot4	solid	yellow	yellow )
( display M4_test9	X	thickLine2	slate	slate )
( display M1_test1	X	thickLine2	cyan	cyan )
( display FLASH2_drawing8	blank	thickLine	magenta	magenta )
( display PO_testb	cross	thickLine	tan	tan )
( display POBLK_dummy	blank	solid	orange	orange )
( display RV_drawingd	x	solid	forest	forest )
( display ICOVL_drawing8	cross	solid	green	green )
( display CDUDMY_test2	cross	solid	green	green )
( display ICOVL_dummyb	cross	solid	green	green )
( display M9_dummyj	X	thickLine2	lime	lime )
( display FLASH_drawing8	blank	solid	lime	lime )
( display ICOVL_dummy5	cross	solid	green	green )
( display RRAM_drawing7	blank	solid	red	red )
( display POS_dummy5	brick	dashDot	cyan	cyan )
( display M8_teste	X	thickLine2	cream	cream )
( display MFUSE_drawing3	dot4	solid	blue	blue )
( display RAM1TDMY_drawing	dot2	solid	blue	blue )
( display BTPM_drawing	blank	solid	red	red )
( display INDDMY_dummye	blank	solid	green	green )
( display VIA6_boundary	blank	solid	white	white )
( display HV_dummy4	blank	thickLine	magenta	magenta )
( display M3_testh	X	thickLine2	forest	forest )
( display VIAEXCL_dummy7	blank	solid	violet	violet )
( display CTMDMY_drawing6	blank	solid	green	green )
( display SRAMDMY_drawing3	solid	solid	orange	orange )
( display RFDMY_dummy8	X	thickLine	red	red )
( display M9_testi	X	thickLine2	lime	lime )
( display MOMDMY_test9	blank	solid	orange	orange )
( display RFDMY_drawingc	blank	dashed	purple	purple )
( display M4_dummyk	X	thickLine2	slate	slate )
( display SRM_DOD_dummyf	blank	solid	red	red )
( display CBD_drawing	blank	solid	lime	lime )
( display MOMDMY_dummyf	blank	solid	orange	orange )
( display M9_boundary	dot4	solid	white	white )
( display RV_drawing	x	solid	forest	forest )
( display CLDD_drawing1	blank	solid	white	white )
( display M7_text	X	thickLine2	cyan	cyan )
( display M3_dummyl	X	thickLine2	forest	forest )
( display M7_dummy1	X	thickLine2	lime	lime )
( display RRAM_drawing9	blank	solid	red	red )
( display MRDMY_dummyr	blank	solid	yellow	yellow )
( display M7_dummyh	X	thickLine2	lime	lime )
( display CELLADD_drawing	blank	solid	red	red )
( display M6_dummy4	X	thickLine2	tan	tan )
( display BSV_drawing	blank	solid	red	red )
( display M7_testa	X	thickLine2	lime	lime )
( display UBM_BSL	blank	solid	red	red )
( display TFRDUMMY_7_drawing	blank	dashed	purple	purple )
( display UBM_pin	dot1	thickLine	gray	gray )
( display M6_net	X	solid	white	white )
( display Row_drawing	blank	solid	cyan	cyan )
( display IP_drawing	dot1	thickLine	gray	gray )
( display changedLayer_tool1	blank	solid	yellow	yellow )
( display CBM_drawing2	triangle	solid	orange	orange )
( display FLASH_dummya	blank	solid	lime	lime )
( display FLASH2_dummyk	blank	solid	lime	lime )
( display FINFET_dummye	hLine	solid	lime	lime )
( display M7_dummy7	X	thickLine2	lime	lime )
( display MOMDMY_drawing	blank	solid	joy1	joy1 )
( display M5_test4	X	thickLine2	winColor3	winColor3 )
( display M5_testd	X	thickLine2	winColor3	winColor3 )
( display FLASH1_drawingf	blank	solid	lime	lime )
( display M3_dummy1	X	thickLine2	forest	forest )
( display ICOVL_drawingd	cross	solid	green	green )
( display VIA6_drawinga	dot4	solid	cyan	cyan )
( display M6_dummy9	X	thickLine2	tan	tan )
( display M4_grid	dot4	solid	slate	slate )
( display DCO_drawing1	blank	solid	orange	orange )
( display HVD_drawing1	blank	shortDash	magenta	magenta )
( display RFDMY_drawing8	X	thickLine	red	red )
( display CDUDMY_test7	cross	solid	green	green )
( display SRM_test5	blank	solid	forest	forest )
( display VIA7_pin	solid	solid	yellow	yellow )
( display CDUDMY_drawing4	cross	solid	green	green )
( display OD_test8	backSlash	solid	purple	purple )
( display INDDMY_dummy9	blank	solid	green	green )
( display LDDBLK_drawing	blank	solid	orange	orange )
( display M1_VNV	X	thickLine2	cyan	cyan )
( display hilite_drawing2	blank	solid	tan	tan )
( display M9_dummyc	X	thickLine2	lime	lime )
( display RFDMY_dummyc	X	thickLine	red	red )
( display RAM1TDMY_drawing1	blank	solid	magenta	magenta )
( display M3_dummy6	X	thickLine2	forest	forest )
( display CTMDMY_testa	blank	solid	green	green )
( display PO_pin	X	thickLine	blue	blue )
( display RMDMY_drawingd	brick	dashDot	orange	orange )
( display M0DMY_dummy	blank	thickLine2	blue	blue )
( display y6_drawing	blank	dashed	blueB	blueB )
( display M8_dummy2	X	thickLine2	cream	cream )
( display M7_test7	X	thickLine2	lime	lime )
( display M7_dummyg	X	thickLine2	lime	lime )
( display NT_N_drawing	blank	shortDash	gray	gray )
( display M5_test2	X	thickLine2	winColor3	winColor3 )
( display OD_drain	dagger	solid	red	red )
( display M8_VNV	X	thickLine2	cream	cream )
( display M5_dummyl	X	thickLine2	winColor3	winColor3 )
( display BJTDMY_dummy7	triangle	solid	green	green )
( display RRVDUH_drawing	blank	solid	red	red )
( display MOMDMY_drawinga	blank	dashed	forest	forest )
( display SRM_drawing4	blank	solid	tan	tan )
( display M3_VNV	X	thickLine2	forest	forest )
( display y5_drawing	blank	dashed	greenB	greenB )
( display M8_net	X	solid	white	white )
( display DMEXCL_dummy1	blank	solid	orange	orange )
( display NW_drawing1	blank	thickLine2	cream	cream )
( display VTL_P_drawing2	blank	thickLine2	gray	gray )
( display SRM_dummy9	blank	solid	forest	forest )
( display CTMDMY_drawing2	blank	solid	green	green )
( display SRM_DPO_dummy1	blank	solid	blue	blue )
( display SRM_LOP_dpsrm	dot2	solid	tan	tan )
( display DSDDMY_drawing6	blank	dashed	tan	tan )
( display M5_odummy	dot4	thickLine2	winColor3	winColor3 )
( display M9_dummy1	X	thickLine2	lime	lime )
( display VIA4_drawinga	dot4	solid	cyan	cyan )
( display VIA2_pin	solid	solid	yellow	yellow )
( display M1_testc	X	thickLine2	cyan	cyan )
( display SDI_drawing4	blank	solid	white	white )
( display M8_dummy9	X	thickLine2	cream	cream )
( display PROBEBTPM_drawing	blank	solid	red	red )
( display M7_pin	X	thickLine2	cyan	cyan )
( display DSDDMY_test1	blank	dashed	winColor5	winColor5 )
( display CO_blockage	cross	solid	green	green )
( display AVTP_drawing	blank	solid	white	white )
( display SDI_drawing3	blank	solid	white	white )
( display P3_drawing1	blank	solid	white	white )
( display POS_dummyd	brick	dashDot	cyan	cyan )
( display FTCDBLK_dummy	blank	solid	orange	orange )
( display MRAM_drawing2	X	solid	green	green )
( display M1_dummyk	X	thickLine2	cyan	cyan )
( display FGD_dummy1	blank	solid	navy	navy )
( display M4_dummy2	X	thickLine2	slate	slate )
( display M2_boundary	dot4	solid	white	white )
( display M9_dummyb	X	thickLine2	lime	lime )
( display CBM_drawing	triangle	solid	orange	orange )
( display OD_DECAP_drawing	dot3	thickLine	winColor2	winColor1 )
( display MRDMY_dummyb	dot4	solid	yellow	yellow )
( display MOMDMY_testc	blank	solid	orange	orange )
( display FLASH2_teste	blank	solid	magenta	magenta )
( display CBRAM_drawingf	blank	solid	red	red )
( display MRAM_drawingb	brick	dashDot	blue	blue )
( display HV_pdd	blank	thickLine	magenta	magenta )
( display CAP1TDMY_drawing	blank	solid	green	green )
( display FET_n_a	blank	solid	gray	gray )
( display SRM1_dummyi	blank	solid	forest	forest )
( display AP_test5	dots	solid	gray	gray )
( display AP_boundary	solid	solid	lime	lime )
( display Layer1_drawing2	blank	solid	navy	navy )
( display CO_test1	cross	solid	blue	blue )
( display VIA2_drawinga	dot4	solid	cyan	cyan )
( display POS_drawing3	brick	dashDot	gold	gold )
( display M8_dummy	X	thickLine2	cream	cream )
( display OD_18_drawing1	dagger	thickLine	winColor2	winColor1 )
( display MOMDMY_dummy9	blank	solid	orange	orange )
( display HV_dummyp	blank	solid	navy	navy )
( display SRM1_dummy7	blank	solid	forest	forest )
( display RPDMY_dummy4	blank	dashed	purple	purple )
( display CSRDMY_drawing5	blank	solid	blue	blue )
( display PO_drawing	cross	thickLine	blue	blue )
( display M5_dummy6	X	thickLine2	winColor3	winColor3 )
( display M1_testj	X	thickLine2	cyan	cyan )
( display INDDMY_drawing8	blank	solid	lime	lime )
( display edgeLayer_drawing	blank	solid	winColor5B	winColor5B )
( display SOISUB_drawing	blank	thickLine2	gray	gray )
( display WAIVER_drawing7	blank	solid	red	red )
( display FLASH1_dummyo	blank	solid	lime	lime )
( display MRDMY_test1	dot4	solid	yellow	yellow )
( display designFlow_drawing1	solid	solid	red	red )
( display M2_testb	X	thickLine2	gold	gold )
( display MOMDMY_test0	blank	solid	orange	orange )
( display M1_dummy9	X	thickLine2	cyan	cyan )
( display OD_test1	backSlash	solid	purple	purple )
( display SRM_dummyj	blank	solid	forest	forest )
( display MRDMY_dummy3	dot4	solid	yellow	yellow )
( display ISO_PW_drawing	blank	shortDash	cream	cream )
( display FINFET_dummy1	hLine	solid	lime	lime )
( display TCDDMY_drawing5	cross	solid	green	green )
( display BMZ_dummy1	blank	solid	red	red )
( display FLASH1_dummyj	blank	solid	lime	lime )
( display AP_dummy4	X	thickLine2	gold	gold )
( display FLASH2_drawing1	blank	solid	gold	gold )
( display FLASH_dummyl	blank	solid	lime	lime )
( display SBDDMY_hv	blank	solid	red	red )
( display OD_boundary	solid	solid	lime	lime )
( display RFDMY_drawinga	blank	dashed	purple	purple )
( display RMDMY_drawing7	brick	dashDot	lime	lime )
( display Unrouted_drawing3	blank	dashed	pink	pink )
( display ICOVL_dummyl	cross	solid	green	green )
( display RAM1TDMY_drawing6	blank	solid	magenta	magenta )
( display CBRAM_drawing2	blank	solid	red	red )
( display AP_drawing1	dots	solid	gray	gray )
( display RH_drawing1	X	thickLine2	forest	forest )
( display M9_testd	X	thickLine2	lime	lime )
( display RPDMY_lvs	brick	dashDot	pink	pink )
( display MRDMY_drawing2	dot4	solid	yellow	yellow )
( display FLASH1_drawing3	blank	solid	lime	lime )
( display FLASH2_dummyb	cross	solid	green	green )
( display LMARK_drawing	blank	solid	white	white )
( display DMEXCL_dummyb	blank	solid	orange	orange )
( display NSD_drawing	blank	dashed	red	red )
( display SRM_dummye	blank	solid	forest	forest )
( display MRDMY_drawingd	dot4	solid	yellow	yellow )
( display FLASH_drawingd	blank	solid	lime	lime )
( display MRAM_dummyb	dot4	solid	yellow	yellow )
( display VAR_drawinge	blank	solid	yellow	yellow )
( display CBRAM_drawing9	blank	solid	red	red )
( display SRAMDMY_allsram	blank	solid	tan	tan )
( display MRAM_drawing9	X	thickLine	red	red )
( display M4_test8	X	thickLine2	slate	slate )
( display M4_dummy5	X	thickLine2	slate	slate )
( display SRM_DOD_dummya	blank	solid	red	red )
( display M6_testb	X	thickLine2	tan	tan )
( display M6_dummym	X	thickLine2	tan	tan )
( display VIA7_blockage	vLine	solid	tan	tan )
( display marker_warning	X	solid	yellow	yellowB )
( display TCLO_test6	cross	solid	blue	blue )
( display M9_test2	X	thickLine2	lime	lime )
( display OD25_33P_LL_drawing	blank	solid	red	red )
)

