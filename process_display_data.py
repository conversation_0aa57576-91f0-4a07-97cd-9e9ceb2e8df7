#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理u22_display.txt文件中的drDefinePacket模块数据
将display和psb数据分别存储到Excel文件中
"""

import re
import pandas as pd
from typing import List, Dict, <PERSON>ple
import os

def parse_display_file(file_path: str) -> Tuple[List[Dict], List[Dict]]:
    """
    解析display文件，提取drDefinePacket模块中的display和psb数据
    
    Args:
        file_path: 文件路径
        
    Returns:
        tuple: (display_data, psb_data) 两个列表，包含解析后的数据
    """
    display_data = []
    psb_data = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找drDefinePacket模块
    packet_pattern = r'drDefinePacket\(\s*;.*?\n(.*?)\)'
    packet_matches = re.findall(packet_pattern, content, re.DOTALL)
    
    for packet_content in packet_matches:
        # 解析每一行数据
        lines = packet_content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith(';'):
                continue
                
            # 使用正则表达式解析数据行
            # 格式: ( DisplayName  PacketName  Stipple  LineStyle  Fill  Outline  [FillStyle])
            pattern = r'\(\s*(\w+)\s+(\w+)\s+(\w+)\s+(\w+)\s+(\w+)\s+(\w+)\s+(\w+)\s*\)'
            match = re.match(pattern, line)
            
            if match:
                display_name, packet_name, stipple, line_style, fill, outline, fill_style = match.groups()
                
                data_dict = {
                    'DisplayName': display_name,
                    'PacketName': packet_name,
                    'Stipple': stipple,
                    'LineStyle': line_style,
                    'Fill': fill,
                    'Outline': outline,
                    'FillStyle': fill_style
                }
                
                # 根据DisplayName分类
                if display_name == 'display':
                    display_data.append(data_dict)
                elif display_name == 'psb':
                    psb_data.append(data_dict)
    
    return display_data, psb_data

def parse_color_data(file_path: str) -> Tuple[List[Dict], List[Dict]]:
    """
    解析颜色定义数据
    
    Args:
        file_path: 文件路径
        
    Returns:
        tuple: (display_colors, psb_colors)
    """
    display_colors = []
    psb_colors = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找drDefineColor模块
    color_pattern = r'drDefineColor\(\s*;.*?\n(.*?)\)'
    color_matches = re.findall(color_pattern, content, re.DOTALL)
    
    for color_content in color_matches:
        lines = color_content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith(';'):
                continue
                
            # 解析颜色数据行
            # 格式: ( DisplayName   ColorsName   Red      Green    Blue     )
            pattern = r'\(\s*(\w+)\s+(\w+)\s+(\d+)\s+(\d+)\s+(\d+)\s*(?:\s+(\w+))?\s*\)'
            match = re.match(pattern, line)
            
            if match:
                display_name, color_name, red, green, blue = match.groups()[:5]
                blink = match.group(6) if match.group(6) else None
                
                color_dict = {
                    'DisplayName': display_name,
                    'ColorName': color_name,
                    'Red': int(red),
                    'Green': int(green),
                    'Blue': int(blue),
                    'Blink': blink
                }
                
                if display_name == 'display':
                    display_colors.append(color_dict)
                elif display_name == 'psb':
                    psb_colors.append(color_dict)
    
    return display_colors, psb_colors

def parse_stipple_data(file_path: str) -> Tuple[List[Dict], List[Dict]]:
    """
    解析点阵图案定义数据
    
    Args:
        file_path: 文件路径
        
    Returns:
        tuple: (display_stipples, psb_stipples)
    """
    display_stipples = []
    psb_stipples = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找drDefineStipple模块
    stipple_pattern = r'drDefineStipple\(\s*;.*?\n(.*?)\)'
    stipple_matches = re.findall(stipple_pattern, content, re.DOTALL)
    
    for stipple_content in stipple_matches:
        lines = stipple_content.strip().split('\n')
        i = 0
        
        while i < len(lines):
            line = lines[i].strip()
            if not line or line.startswith(';'):
                i += 1
                continue
            
            # 查找stipple定义的开始
            start_pattern = r'\(\s*(\w+)\s+(\w+)\s+\('
            match = re.match(start_pattern, line)
            
            if match:
                display_name, stipple_name = match.groups()
                
                # 收集bitmap数据
                bitmap_lines = []
                i += 1
                
                while i < len(lines):
                    bitmap_line = lines[i].strip()
                    if bitmap_line.endswith(') ) )'):
                        # 最后一行
                        bitmap_line = bitmap_line.replace(') ) )', '').strip()
                        if bitmap_line.startswith('('):
                            bitmap_lines.append(bitmap_line)
                        break
                    elif bitmap_line.startswith('(') and bitmap_line.endswith(')'):
                        bitmap_lines.append(bitmap_line)
                    i += 1
                
                stipple_dict = {
                    'DisplayName': display_name,
                    'StippleName': stipple_name,
                    'BitmapLines': len(bitmap_lines),
                    'BitmapData': ' | '.join(bitmap_lines[:3]) + '...' if len(bitmap_lines) > 3 else ' | '.join(bitmap_lines)
                }
                
                if display_name == 'display':
                    display_stipples.append(stipple_dict)
                elif display_name == 'psb':
                    psb_stipples.append(stipple_dict)
            
            i += 1
    
    return display_stipples, psb_stipples

def parse_line_style_data(file_path: str) -> Tuple[List[Dict], List[Dict]]:
    """
    解析线型定义数据
    
    Args:
        file_path: 文件路径
        
    Returns:
        tuple: (display_line_styles, psb_line_styles)
    """
    display_line_styles = []
    psb_line_styles = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找drDefineLineStyle模块
    line_style_pattern = r'drDefineLineStyle\(\s*;.*?\n(.*?)\)'
    line_style_matches = re.findall(line_style_pattern, content, re.DOTALL)
    
    for line_style_content in line_style_matches:
        lines = line_style_content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith(';'):
                continue
                
            # 解析线型数据行
            # 格式: ( DisplayName   LineStyle    Size       Pattern   )
            pattern = r'\(\s*(\w+)\s+(\w+)\s+(\d+)\s+\((.*?)\)\s*\)'
            match = re.match(pattern, line)
            
            if match:
                display_name, line_style_name, size, pattern = match.groups()
                
                line_style_dict = {
                    'DisplayName': display_name,
                    'LineStyleName': line_style_name,
                    'Size': int(size),
                    'Pattern': pattern.strip()
                }
                
                if display_name == 'display':
                    display_line_styles.append(line_style_dict)
                elif display_name == 'psb':
                    psb_line_styles.append(line_style_dict)
    
    return display_line_styles, psb_line_styles

def save_to_excel(display_data: Dict, psb_data: Dict, output_file: str):
    """
    将数据保存到Excel文件中
    
    Args:
        display_data: display相关数据字典
        psb_data: psb相关数据字典
        output_file: 输出文件路径
    """
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # Display数据
        if display_data['packets']:
            df_display_packets = pd.DataFrame(display_data['packets'])
            df_display_packets.to_excel(writer, sheet_name='Display_Packets', index=False)
        
        if display_data['colors']:
            df_display_colors = pd.DataFrame(display_data['colors'])
            df_display_colors.to_excel(writer, sheet_name='Display_Colors', index=False)
        
        if display_data['stipples']:
            df_display_stipples = pd.DataFrame(display_data['stipples'])
            df_display_stipples.to_excel(writer, sheet_name='Display_Stipples', index=False)
        
        if display_data['line_styles']:
            df_display_line_styles = pd.DataFrame(display_data['line_styles'])
            df_display_line_styles.to_excel(writer, sheet_name='Display_LineStyles', index=False)
        
        # PSB数据
        if psb_data['packets']:
            df_psb_packets = pd.DataFrame(psb_data['packets'])
            df_psb_packets.to_excel(writer, sheet_name='PSB_Packets', index=False)
        
        if psb_data['colors']:
            df_psb_colors = pd.DataFrame(psb_data['colors'])
            df_psb_colors.to_excel(writer, sheet_name='PSB_Colors', index=False)
        
        if psb_data['stipples']:
            df_psb_stipples = pd.DataFrame(psb_data['stipples'])
            df_psb_stipples.to_excel(writer, sheet_name='PSB_Stipples', index=False)
        
        if psb_data['line_styles']:
            df_psb_line_styles = pd.DataFrame(psb_data['line_styles'])
            df_psb_line_styles.to_excel(writer, sheet_name='PSB_LineStyles', index=False)

def main():
    """主函数"""
    input_file = 'u22_display.txt'
    output_file = 'u22_display_data.xlsx'
    
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        return
    
    print("开始处理文件...")
    
    # 解析各种数据
    display_packets, psb_packets = parse_display_file(input_file)
    display_colors, psb_colors = parse_color_data(input_file)
    display_stipples, psb_stipples = parse_stipple_data(input_file)
    display_line_styles, psb_line_styles = parse_line_style_data(input_file)
    
    # 组织数据
    display_data = {
        'packets': display_packets,
        'colors': display_colors,
        'stipples': display_stipples,
        'line_styles': display_line_styles
    }
    
    psb_data = {
        'packets': psb_packets,
        'colors': psb_colors,
        'stipples': psb_stipples,
        'line_styles': psb_line_styles
    }
    
    # 打印统计信息
    print(f"\n数据统计:")
    print(f"Display - Packets: {len(display_packets)}, Colors: {len(display_colors)}, Stipples: {len(display_stipples)}, LineStyles: {len(display_line_styles)}")
    print(f"PSB - Packets: {len(psb_packets)}, Colors: {len(psb_colors)}, Stipples: {len(psb_stipples)}, LineStyles: {len(psb_line_styles)}")
    
    # 保存到Excel
    save_to_excel(display_data, psb_data, output_file)
    print(f"\n数据已成功保存到 {output_file}")
    
    # 显示前几条数据作为示例
    if display_packets:
        print(f"\nDisplay Packets 示例 (前3条):")
        for i, packet in enumerate(display_packets[:3]):
            print(f"  {i+1}. {packet}")
    
    if psb_packets:
        print(f"\nPSB Packets 示例 (前3条):")
        for i, packet in enumerate(psb_packets[:3]):
            print(f"  {i+1}. {packet}")

if __name__ == "__main__":
    main()
