#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的Excel文件内容
"""

import pandas as pd
import os

def verify_excel_file(file_path):
    """验证Excel文件内容"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"Excel文件: {file_path}")
        print(f"包含工作表: {sheet_names}")
        print("=" * 50)
        
        for sheet_name in sheet_names:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"\n工作表: {sheet_name}")
            print(f"行数: {len(df)}")
            print(f"列数: {len(df.columns)}")
            print(f"列名: {list(df.columns)}")
            
            if len(df) > 0:
                print("前3行数据:")
                print(df.head(3).to_string(index=False))
            print("-" * 40)
    
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")

def main():
    excel_file = 'u22_display_complete.xlsx'
    verify_excel_file(excel_file)

if __name__ == "__main__":
    main()
